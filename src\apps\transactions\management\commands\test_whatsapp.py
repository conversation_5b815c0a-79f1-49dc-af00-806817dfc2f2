"""
Django management command to test WhatsApp integration functionality.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.customers.models import Customer
from apps.transactions.models import Transaction, TransactionType
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.transactions.views import TransactionViewSet
from apps.transactions.serializers import TransactionListSerializer

User = get_user_model()

class Command(BaseCommand):
    help = 'Test WhatsApp integration functionality'

    def handle(self, *args, **options):
        """Test the WhatsApp integration functionality."""
        self.stdout.write("Testing WhatsApp Integration...")
        
        try:
            # Get or create test data
            location = Location.objects.first()
            if not location:
                self.stdout.write(self.style.ERROR("❌ No locations found. Please run migrations and setup."))
                return
                
            currency_usd = Currency.objects.filter(code='USD').first()
            currency_aed = Currency.objects.filter(code='AED').first()
            
            if not currency_usd or not currency_aed:
                self.stdout.write(self.style.ERROR("❌ Required currencies (USD, AED) not found."))
                return
                
            transaction_type = TransactionType.objects.first()
            if not transaction_type:
                self.stdout.write(self.style.ERROR("❌ No transaction types found."))
                return
            
            # Create test customer with WhatsApp group ID
            customer = Customer.objects.create(
                customer_type='individual',
                first_name='Test',
                last_name='Customer',
                phone_number='+1234567890',
                email='<EMAIL>',
                whatsapp_group_id='test-group-123',
                status='active'
            )
            self.stdout.write(self.style.SUCCESS(f"✅ Created test customer: {customer.get_display_name()}"))
            
            # Create test transaction
            transaction = Transaction.objects.create(
                transaction_type=transaction_type,
                customer=customer,
                location=location,
                from_currency=currency_usd,
                to_currency=currency_aed,
                from_amount=1000.00,
                to_amount=3673.00,
                exchange_rate=3.673,
                status='approved'
            )
            self.stdout.write(self.style.SUCCESS(f"✅ Created test transaction: {transaction.transaction_number}"))
            
            # Test WhatsApp message generation
            viewset = TransactionViewSet()
            message = viewset._generate_transaction_message(transaction)
            
            self.stdout.write("\n📱 Generated WhatsApp Message:")
            self.stdout.write("-" * 50)
            self.stdout.write(message)
            self.stdout.write("-" * 50)
            
            # Test customer without WhatsApp group ID
            customer_no_whatsapp = Customer.objects.create(
                customer_type='individual',
                first_name='No WhatsApp',
                last_name='Customer',
                phone_number='+9876543210',
                email='<EMAIL>',
                whatsapp_group_id='',  # Empty WhatsApp group ID
                status='active'
            )
            
            transaction_no_whatsapp = Transaction.objects.create(
                transaction_type=transaction_type,
                customer=customer_no_whatsapp,
                location=location,
                from_currency=currency_usd,
                to_currency=currency_aed,
                from_amount=500.00,
                to_amount=1836.50,
                exchange_rate=3.673,
                status='approved'
            )
            
            self.stdout.write(self.style.SUCCESS(f"✅ Created customer without WhatsApp: {customer_no_whatsapp.get_display_name()}"))
            self.stdout.write(self.style.SUCCESS(f"✅ Created transaction for customer without WhatsApp: {transaction_no_whatsapp.transaction_number}"))
            
            # Test serializer fields
            serializer = TransactionListSerializer(transaction)
            data = serializer.data
            
            self.stdout.write(f"\n🔍 Serializer Test Results:")
            self.stdout.write(f"   - has_whatsapp: {data.get('has_whatsapp')}")
            self.stdout.write(f"   - customer_whatsapp_group_id: {data.get('customer_whatsapp_group_id')}")
            
            serializer_no_whatsapp = TransactionListSerializer(transaction_no_whatsapp)
            data_no_whatsapp = serializer_no_whatsapp.data
            
            self.stdout.write(f"   - has_whatsapp (no WhatsApp): {data_no_whatsapp.get('has_whatsapp')}")
            self.stdout.write(f"   - customer_whatsapp_group_id (no WhatsApp): {data_no_whatsapp.get('customer_whatsapp_group_id')}")
            
            # Cleanup
            transaction.delete()
            transaction_no_whatsapp.delete()
            customer.delete()
            customer_no_whatsapp.delete()
            
            self.stdout.write(self.style.SUCCESS("\n✅ All tests passed! WhatsApp integration is working correctly."))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed with error: {str(e)}"))
            import traceback
            traceback.print_exc()
