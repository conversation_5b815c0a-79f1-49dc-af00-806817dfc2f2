2025-08-16 05:32:12,462 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:32:12,462 - apps.customers.models - INFO - Customer saved: CUS000001 - Turkish Exports Ltd 1
2025-08-16 05:32:58,380 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:32:58,381 - apps.customers.models - INFO - Customer saved: CUS000001 - <PERSON>
2025-08-16 05:36:15,783 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:36:16,131 - apps.customers.models - INFO - Customer saved: CUS000001 - <PERSON>
2025-08-16 05:36:16,917 - apps.customers.signals - INFO - New customer created: CUS000002
2025-08-16 05:36:17,236 - apps.customers.models - INFO - Customer saved: CUS000002 - Istanbul Business Center 2
2025-08-16 05:36:18,716 - apps.customers.models - INFO - Customer document saved: Passport - Istanbul Business Center 2 for CUS000002
2025-08-16 05:36:18,966 - apps.customers.signals - INFO - New customer created: CUS000003
2025-08-16 05:36:19,284 - apps.customers.models - INFO - Customer saved: CUS000003 - Ahmed Al-Rashid
2025-08-16 05:36:20,391 - apps.customers.models - INFO - Customer document saved: Tax Certificate - Ahmed Al-Rashid for CUS000003
2025-08-16 05:36:21,247 - apps.customers.models - INFO - Customer document saved: Business License - Ahmed Al-Rashid for CUS000003
2025-08-16 05:36:21,593 - apps.customers.signals - INFO - New customer created: CUS000004
2025-08-16 05:36:22,048 - apps.customers.models - INFO - Customer saved: CUS000004 - Ahmed Al-Rashid
2025-08-16 05:36:23,129 - apps.customers.models - INFO - Customer document saved: Tax Certificate - Ahmed Al-Rashid for CUS000004
2025-08-16 05:36:23,551 - apps.customers.models - INFO - Customer document saved: Passport - Ahmed Al-Rashid for CUS000004
2025-08-16 05:36:23,823 - apps.customers.signals - INFO - New customer created: CUS000005
2025-08-16 05:36:24,119 - apps.customers.models - INFO - Customer saved: CUS000005 - European Business Solutions 5
2025-08-16 05:36:25,029 - apps.customers.models - INFO - Customer document saved: Tax Certificate - European Business Solutions 5 for CUS000005
2025-08-16 05:36:25,390 - apps.customers.signals - INFO - New customer created: CUS000006
2025-08-16 05:36:25,601 - apps.customers.models - INFO - Customer saved: CUS000006 - Istanbul Business Center 6
2025-08-16 05:36:26,580 - apps.customers.models - INFO - Customer document saved: Business License - Istanbul Business Center 6 for CUS000006
2025-08-16 05:36:26,884 - apps.customers.models - INFO - Customer document saved: Tax Certificate - Istanbul Business Center 6 for CUS000006
2025-08-16 05:36:27,058 - apps.customers.signals - INFO - New customer created: CUS000007
2025-08-16 05:36:27,390 - apps.customers.models - INFO - Customer saved: CUS000007 - Turkish Exports Ltd 7
2025-08-16 05:36:28,052 - apps.customers.signals - INFO - New customer created: CUS000008
2025-08-16 05:36:37,876 - apps.customers.signals - INFO - New customer created: CUS000009
2025-08-16 05:36:38,236 - apps.customers.models - INFO - Customer saved: CUS000009 - Jane Doe
2025-08-16 05:36:39,368 - apps.customers.models - INFO - Customer document saved: Id Card - Jane Doe for CUS000009
2025-08-16 05:36:39,718 - apps.customers.models - INFO - Customer document saved: Passport - Jane Doe for CUS000009
2025-08-16 05:36:39,829 - apps.customers.signals - INFO - New customer created: CUS000010
2025-08-16 05:36:40,067 - apps.customers.models - INFO - Customer saved: CUS000010 - European Business Solutions 10
2025-08-16 05:36:40,926 - apps.customers.models - INFO - Customer document saved: Business License - European Business Solutions 10 for CUS000010
2025-08-16 05:36:41,457 - apps.customers.models - INFO - Customer document saved: Passport - European Business Solutions 10 for CUS000010
2025-08-16 05:47:28,083 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:47:28,084 - apps.customers.models - INFO - Customer saved: CUS000001 - Turkish Exports Ltd 1
2025-08-16 05:47:28,327 - apps.customers.models - INFO - Customer document saved: Passport - Turkish Exports Ltd 1 for CUS000001
2025-08-16 05:47:28,328 - apps.customers.signals - INFO - New customer created: CUS000002
2025-08-16 05:47:28,329 - apps.customers.models - INFO - Customer saved: CUS000002 - Istanbul Business Center 2
2025-08-16 05:47:28,335 - apps.customers.models - INFO - Customer document saved: Tax Certificate - Istanbul Business Center 2 for CUS000002
2025-08-16 05:47:28,337 - apps.customers.signals - INFO - New customer created: CUS000003
2025-08-16 05:47:28,588 - apps.customers.signals - INFO - New customer created: CUS000004
2025-08-16 05:47:28,589 - apps.customers.models - INFO - Customer saved: CUS000004 - Omar Hassan
2025-08-16 05:47:28,650 - apps.customers.signals - INFO - New customer created: CUS000005
2025-08-16 05:48:10,985 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:48:11,053 - apps.customers.signals - INFO - New customer created: CUS000002
2025-08-16 05:48:11,053 - apps.customers.models - INFO - Customer saved: CUS000002 - UAE Trading House 2
2025-08-16 05:48:11,139 - apps.customers.models - INFO - Customer document saved: Passport - UAE Trading House 2 for CUS000002
2025-08-16 05:48:11,140 - apps.customers.signals - INFO - New customer created: CUS000003
2025-08-16 05:48:11,141 - apps.customers.models - INFO - Customer saved: CUS000003 - Emily Wilson
2025-08-16 05:48:11,145 - apps.customers.models - INFO - Customer document saved: Tax Certificate - Emily Wilson for CUS000003
2025-08-16 05:48:11,147 - apps.customers.models - INFO - Customer document saved: Business License - Emily Wilson for CUS000003
2025-08-16 05:48:55,108 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:48:55,109 - apps.customers.models - INFO - Customer saved: CUS000001 - David Brown
2025-08-16 05:48:55,116 - apps.customers.models - INFO - Customer document saved: Id Card - David Brown for CUS000001
2025-08-16 05:48:55,118 - apps.customers.models - INFO - Customer document saved: Tax Certificate - David Brown for CUS000001
2025-08-16 05:48:55,119 - apps.customers.signals - INFO - New customer created: CUS000002
2025-08-16 05:48:55,120 - apps.customers.models - INFO - Customer saved: CUS000002 - European Business Solutions 2
2025-08-16 05:48:55,161 - apps.customers.models - INFO - Customer document saved: Id Card - European Business Solutions 2 for CUS000002
2025-08-16 05:48:55,162 - apps.customers.signals - INFO - New customer created: CUS000003
2025-08-16 05:48:55,164 - apps.customers.models - INFO - Customer saved: CUS000003 - Mustafa Kaya
2025-08-16 05:49:44,086 - apps.customers.signals - INFO - New customer created: CUS000001
2025-08-16 05:49:44,087 - apps.customers.models - INFO - Customer saved: CUS000001 - Shanghai Trading Co 1
2025-08-16 05:49:44,147 - apps.customers.signals - INFO - New customer created: CUS000002
2025-08-16 05:49:44,600 - apps.customers.signals - INFO - New customer created: CUS000003
2025-08-16 05:49:44,601 - apps.customers.models - INFO - Customer saved: CUS000003 - Fatima Al-Zahra
2025-08-17 01:05:42,251 - apps.customers.views - ERROR - Error getting customer stats for CUS000002: Cannot resolve keyword 'amount' into field. Choices are: approved_at, approved_by, approved_by_id, balance_entries, child_transactions, commission_amount, commission_currency, commission_currency_id, completed_at, courier, courier_id, created_at, created_by, created_by_id, customer, customer_id, deleted_at, deleted_by, deleted_by_id, delivery_address, delivery_method, description, documents, exchange_rate, from_amount, from_currency, from_currency_id, id, is_deleted, location, location_id, notes, parent_transaction, parent_transaction_id, reference_number, status, step_number, to_amount, to_currency, to_currency_id, total_steps, tracking_code, transaction_number, transaction_type, transaction_type_id, updated_at, updated_by, updated_by_id
2025-08-17 01:10:44,321 - apps.customers.views - ERROR - Error getting customer stats for CUS000001: Cannot resolve keyword 'amount' into field. Choices are: approved_at, approved_by, approved_by_id, balance_entries, child_transactions, commission_amount, commission_currency, commission_currency_id, completed_at, courier, courier_id, created_at, created_by, created_by_id, customer, customer_id, deleted_at, deleted_by, deleted_by_id, delivery_address, delivery_method, description, documents, exchange_rate, from_amount, from_currency, from_currency_id, id, is_deleted, location, location_id, notes, parent_transaction, parent_transaction_id, reference_number, status, step_number, to_amount, to_currency, to_currency_id, total_steps, tracking_code, transaction_number, transaction_type, transaction_type_id, updated_at, updated_by, updated_by_id
2025-08-18 01:10:06,067 - apps.customers.views - ERROR - Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
2025-08-18 01:10:21,044 - apps.customers.views - ERROR - Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
2025-08-18 01:10:30,124 - apps.customers.views - ERROR - Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
2025-08-18 01:10:55,867 - apps.customers.views - WARNING - Missing required fields: ['company_name']
2025-08-18 01:11:08,857 - apps.customers.views - ERROR - Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
2025-08-18 01:11:20,786 - apps.customers.views - ERROR - Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
2025-08-18 01:45:20,779 - apps.customers.views - WARNING - Missing required fields: ['phone']
2025-08-18 01:45:41,753 - apps.customers.views - WARNING - Missing required fields: ['phone']
2025-08-18 02:52:37,296 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-18 02:52:37,298 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-18 02:52:37,305 - apps.customers.views - INFO - Customer created: CUST000001 by admin
2025-08-20 04:29:12,956 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-20 04:29:12,957 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-20 04:29:12,959 - apps.customers.signals - INFO - New customer created: CUST000002
2025-08-20 04:29:56,761 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-20 04:29:56,763 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-20 04:29:56,765 - apps.customers.signals - INFO - New customer created: CUST000002
2025-08-20 04:30:31,148 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-20 04:30:31,164 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-20 04:30:31,166 - apps.customers.signals - INFO - New customer created: CUST000002
2025-08-20 04:31:27,459 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-20 04:31:27,460 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-20 04:31:27,462 - apps.customers.signals - INFO - New customer created: CUST000002
2025-08-20 04:31:57,326 - apps.customers.signals - INFO - New customer created: CUST000001
2025-08-20 04:31:57,327 - apps.customers.models - INFO - Customer saved: CUST000001 - Amirhossein Daadbin
2025-08-20 04:31:57,329 - apps.customers.signals - INFO - New customer created: CUST000002
2025-08-21 03:13:18,396 - apps.customers.signals - INFO - New customer created: CUST000005
2025-08-21 03:13:18,397 - apps.customers.models - INFO - Customer saved: CUST000005 - Test Customer
2025-08-21 03:13:18,453 - apps.customers.signals - INFO - New customer created: CUST000006
2025-08-21 03:13:18,454 - apps.customers.models - INFO - Customer saved: CUST000006 - No WhatsApp Customer
2025-08-21 03:13:18,520 - apps.customers.signals - INFO - Customer updated: CUST000005
2025-08-21 03:13:18,521 - apps.customers.models - INFO - Customer saved: CUST000005 - Test Customer
2025-08-21 03:13:18,524 - apps.customers.signals - INFO - Customer updated: CUST000006
2025-08-21 03:13:18,524 - apps.customers.models - INFO - Customer saved: CUST000006 - No WhatsApp Customer
