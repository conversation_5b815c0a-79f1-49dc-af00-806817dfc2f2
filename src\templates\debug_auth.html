{% extends 'base.html' %}
{% load i18n %}

{% block title %}Authentication Debug - Arena Doviz{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bug"></i>
                        Authentication Debug Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Django Session Authentication</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>User Authenticated:</span>
                                    <span class="badge bg-{% if user.is_authenticated %}success{% else %}danger{% endif %}">
                                        {% if user.is_authenticated %}Yes{% else %}No{% endif %}
                                    </span>
                                </li>
                                {% if user.is_authenticated %}
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Username:</span>
                                    <span>{{ user.username }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Role:</span>
                                    <span>{{ user.role }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Is Active:</span>
                                    <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                                        {% if user.is_active %}Yes{% else %}No{% endif %}
                                    </span>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>JWT Token Status</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Access Token:</span>
                                    <span id="jwt-access-status" class="badge bg-secondary">Checking...</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Refresh Token:</span>
                                    <span id="jwt-refresh-status" class="badge bg-secondary">Checking...</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Token Valid:</span>
                                    <span id="jwt-valid-status" class="badge bg-secondary">Checking...</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>API Test Results</h6>
                            <div id="api-test-results">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Testing APIs...</span>
                                    </div>
                                    <p class="mt-2">Testing API endpoints...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>Actions</h6>
                            <button id="get-jwt-tokens" class="btn btn-primary me-2">Get JWT Tokens</button>
                            <button id="test-customers-api" class="btn btn-info me-2">Test Customers API</button>
                            <button id="test-transactions-api" class="btn btn-info me-2">Test Transactions API</button>
                            <button id="clear-tokens" class="btn btn-warning">Clear Tokens</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    checkJWTStatus();
    testAPIs();
    
    // Button handlers
    $('#get-jwt-tokens').click(getJWTTokens);
    $('#test-customers-api').click(testCustomersAPI);
    $('#test-transactions-api').click(testTransactionsAPI);
    $('#clear-tokens').click(clearTokens);
});

function checkJWTStatus() {
    const accessToken = ArenaDoviz.auth.getAccessToken();
    const refreshToken = ArenaDoviz.auth.getRefreshToken();
    
    $('#jwt-access-status').removeClass('bg-secondary bg-success bg-danger')
        .addClass(accessToken ? 'bg-success' : 'bg-danger')
        .text(accessToken ? 'Present' : 'Missing');
    
    $('#jwt-refresh-status').removeClass('bg-secondary bg-success bg-danger')
        .addClass(refreshToken ? 'bg-success' : 'bg-danger')
        .text(refreshToken ? 'Present' : 'Missing');
    
    // Test token validity
    if (accessToken) {
        fetch('/api/v1/accounts/jwt/token/verify/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
            },
            body: JSON.stringify({ token: accessToken })
        })
        .then(response => {
            $('#jwt-valid-status').removeClass('bg-secondary bg-success bg-danger')
                .addClass(response.ok ? 'bg-success' : 'bg-danger')
                .text(response.ok ? 'Valid' : 'Invalid');
        })
        .catch(error => {
            $('#jwt-valid-status').removeClass('bg-secondary bg-success bg-danger')
                .addClass('bg-danger')
                .text('Error');
        });
    } else {
        $('#jwt-valid-status').removeClass('bg-secondary bg-success bg-danger')
            .addClass('bg-warning')
            .text('No Token');
    }
}

function testAPIs() {
    const results = $('#api-test-results');
    results.html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Testing APIs...</p></div>');
    
    const tests = [
        { name: 'Customers API', url: '/api/v1/customers/customers/' },
        { name: 'Transactions API', url: '/api/v1/transactions/transactions/' },
        { name: 'Transfer Transactions', url: '/api/v1/transactions/transactions/?transaction_type_code=TRANSFER' }
    ];
    
    Promise.all(tests.map(test => {
        const headers = ArenaDoviz.auth.getAuthHeaders();
        return fetch(test.url, { headers })
            .then(response => ({
                name: test.name,
                status: response.status,
                ok: response.ok
            }))
            .catch(error => ({
                name: test.name,
                status: 'Error',
                ok: false,
                error: error.message
            }));
    }))
    .then(testResults => {
        let html = '<div class="list-group">';
        testResults.forEach(result => {
            const badgeClass = result.ok ? 'bg-success' : 'bg-danger';
            const statusText = result.ok ? `${result.status} OK` : `${result.status} Failed`;
            html += `
                <div class="list-group-item d-flex justify-content-between">
                    <span>${result.name}:</span>
                    <span class="badge ${badgeClass}">${statusText}</span>
                </div>
            `;
        });
        html += '</div>';
        results.html(html);
    });
}

function getJWTTokens() {
    ArenaDoviz.auth.getJWTTokensForSessionUser()
        .then(data => {
            alert('JWT tokens obtained successfully!');
            checkJWTStatus();
            testAPIs();
        })
        .catch(error => {
            alert('Failed to get JWT tokens: ' + error.message);
        });
}

function testCustomersAPI() {
    const headers = ArenaDoviz.auth.getAuthHeaders();
    fetch('/api/v1/customers/customers/', { headers })
        .then(response => response.json())
        .then(data => {
            alert(`Customers API: ${data.results.length} customers found`);
        })
        .catch(error => {
            alert('Customers API failed: ' + error.message);
        });
}

function testTransactionsAPI() {
    const headers = ArenaDoviz.auth.getAuthHeaders();
    fetch('/api/v1/transactions/transactions/', { headers })
        .then(response => response.json())
        .then(data => {
            alert(`Transactions API: ${data.results.length} transactions found`);
        })
        .catch(error => {
            alert('Transactions API failed: ' + error.message);
        });
}

function clearTokens() {
    ArenaDoviz.auth.clearTokens();
    alert('Tokens cleared!');
    checkJWTStatus();
    testAPIs();
}
</script>
{% endblock %}
