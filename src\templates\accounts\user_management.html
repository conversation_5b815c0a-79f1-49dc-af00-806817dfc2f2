{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "User Management" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
.metric-card {
    border: none !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.metric-card .card-title,
.metric-card h2,
.metric-card small,
.metric-card i {
    color: #000000 !important;
}

.metric-card[style*="#000d28"] .card-title,
.metric-card[style*="#000d28"] h2,
.metric-card[style*="#000d28"] small,
.metric-card[style*="#000d28"] i {
    color: #000000 !important;
}

.metric-card[style*="#013121"] .card-title,
.metric-card[style*="#013121"] h2,
.metric-card[style*="#013121"] small,
.metric-card[style*="#013121"] i {
    color: #000000 !important;
}

.metric-card[style*="#6a0000"] .card-title,
.metric-card[style*="#6a0000"] h2,
.metric-card[style*="#6a0000"] small,
.metric-card[style*="#6a0000"] i {
    color: #000000 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-people"></i>
                {% trans "User Management" %}
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-primary" id="add-user">
                    <i class="bi bi-person-plus"></i>
                    {% trans "New User" %}
                </button>
                <button class="btn btn-outline-secondary" id="user-stats">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Statistics" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #000d28;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Total Users" %}</h5>
                        <h2 class="mb-0 text-white" id="total-users">-</h2>
                        <small class="opacity-75">{% trans "All users" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #013121;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Active Users" %}</h5>
                        <h2 class="mb-0 text-white" id="active-users">-</h2>
                        <small class="opacity-75">{% trans "Currently active" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #6a0000;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Recent Logins" %}</h5>
                        <h2 class="mb-0 text-white" id="recent-logins">-</h2>
                        <small class="opacity-75">{% trans "Last 24 hours" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock-history fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #dc3545;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Locked Accounts" %}</h5>
                        <h2 class="mb-0 text-white" id="locked-accounts">-</h2>
                        <small class="opacity-75">{% trans "Currently locked" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-lock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="role-filter" class="form-label">{% trans "Role" %}</label>
                        <select class="form-select" id="role-filter" name="role">
                            <option value="">{% trans "All Roles" %}</option>
                            <option value="admin">{% trans "Admin" %}</option>
                            <option value="accountant">{% trans "Accountant" %}</option>
                            <option value="branch_employee">{% trans "Branch Employee" %}</option>
                            <option value="viewer">{% trans "Viewer" %}</option>
                            <option value="courier">{% trans "Courier" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="location-filter" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="location-filter" name="location">
                            <option value="">{% trans "All Locations" %}</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="active">{% trans "Active" %}</option>
                            <option value="inactive">{% trans "Inactive" %}</option>
                            <option value="locked">{% trans "Locked" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="search-filter" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search-filter" name="search" placeholder="{% trans 'Search by name, username, email...' %}">
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear Filters" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "User List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="users-table">
                        <thead>
                            <tr>
                                <th>{% trans "Username" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Email" %}</th>
                                <th>{% trans "Role" %}</th>
                                <th>{% trans "Location" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Last Login" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">{% trans "User Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="user-form">
                    <input type="hidden" id="user-id">

                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">{% trans "Basic Information" %}</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">{% trans "Username" %} *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">{% trans "Unique username for login" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">{% trans "Email" %} *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="form-text">{% trans "User's email address" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first-name" class="form-label">{% trans "First Name" %} *</label>
                                <input type="text" class="form-control" id="first-name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last-name" class="form-label">{% trans "Last Name" %} *</label>
                                <input type="text" class="form-control" id="last-name" name="last_name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone-number" class="form-label">{% trans "Phone Number" %}</label>
                                <input type="tel" class="form-control" id="phone-number" name="phone_number">
                                <div class="form-text">{% trans "Format: +999999999" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee-id" class="form-label">{% trans "Employee ID" %}</label>
                                <input type="text" class="form-control" id="employee-id" name="employee_id">
                                <div class="form-text">{% trans "Unique employee identifier" %}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Authentication & Permissions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">{% trans "Authentication & Permissions" %}</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">{% trans "Role" %} *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">{% trans "Select role..." %}</option>
                                    <option value="admin">{% trans "Admin" %}</option>
                                    <option value="accountant">{% trans "Accountant" %}</option>
                                    <option value="branch_employee">{% trans "Branch Employee" %}</option>
                                    <option value="viewer">{% trans "Viewer" %}</option>
                                    <option value="courier">{% trans "Courier" %}</option>
                                </select>
                                <div class="form-text">{% trans "User role determines access permissions" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">{% trans "Location" %}</label>
                                <select class="form-select" id="location" name="location">
                                    <option value="">{% trans "Select location..." %}</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                                <div class="form-text">{% trans "Primary work location" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" id="password-section">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">{% trans "Password" %} <span id="password-required">*</span></label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">{% trans "Minimum 8 characters" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password-confirm" class="form-label">{% trans "Confirm Password" %} <span id="password-confirm-required">*</span></label>
                                <input type="password" class="form-control" id="password-confirm" name="password_confirm">
                                <div class="form-text">{% trans "Must match password" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                                    <label class="form-check-label" for="is-active">
                                        {% trans "Active" %}
                                    </label>
                                    <div class="form-text">{% trans "User can log in and access the system" %}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">{% trans "Department" %}</label>
                                <input type="text" class="form-control" id="department" name="department">
                                <div class="form-text">{% trans "Department or division" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        <div class="form-text">{% trans "Internal notes about this user" %}</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-user">{% trans "Save User" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let usersTable;
let isEditMode = false;

$(document).ready(function() {
    // Initialize page
    loadUserStats();
    loadLocations();
    initializeUsersTable();

    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        usersTable.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        usersTable.ajax.reload();
    });

    $('#refresh-btn').on('click', function() {
        usersTable.ajax.reload();
        loadUserStats();
    });

    $('#export-btn').on('click', function() {
        exportUsers();
    });

    $('#add-user').on('click', function() {
        showUserModal();
    });

    $('#save-user').on('click', function() {
        saveUser();
    });

    // Password confirmation validation
    $('#password-confirm').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();

        if (password !== confirmPassword) {
            $(this).addClass('is-invalid');
            $(this).siblings('.form-text').text('{% trans "Passwords do not match" %}').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.form-text').text('{% trans "Must match password" %}').removeClass('text-danger');
        }
    });

    // Role change handler
    $('#role').on('change', function() {
        const role = $(this).val();
        if (role === 'admin') {
            showAlert('warning', '{% trans "Admin users have full system access. Use with caution." %}');
        }
    });
});

function loadUserStats() {
    $.ajax({
        url: '/accounts/api/user-stats/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#total-users').text(data.total_users || 0);
            $('#active-users').text(data.active_users || 0);
            $('#recent-logins').text(data.recent_logins || 0);
            $('#locked-accounts').text(data.locked_accounts || 0);
        },
        error: function(xhr) {
            console.error('Error loading user stats:', xhr);
        }
    });
}

function loadLocations() {
    $.ajax({
        url: '/users/api/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const locationSelects = $('#location-filter, #location');
            locationSelects.each(function() {
                const currentValue = $(this).val();
                $(this).find('option:not(:first)').remove();

                data.locations.forEach(location => {
                    $(this).append(`<option value="${location.id}">${location.name}</option>`);
                });

                if (currentValue) {
                    $(this).val(currentValue);
                }
            });
        },
        error: function(xhr) {
            console.error('Error loading locations:', xhr);
        }
    });
}

function initializeUsersTable() {
    // Check if DataTable already exists and destroy it
    if ($.fn.DataTable.isDataTable('#users-table')) {
        $('#users-table').DataTable().destroy();
        console.log('Existing users DataTable destroyed');
    }

    const columns = [
        {
            data: 'username',
            name: 'username',
            render: function(data, type, row) {
                return `<code class="fw-bold">${data}</code>`;
            }
        },
        {
            data: null,
            name: 'first_name',
            render: function(data, type, row) {
                return `${row.first_name} ${row.last_name}`.trim() || '-';
            }
        },
        {
            data: 'email',
            name: 'email'
        },
        {
            data: 'role',
            name: 'role',
            render: function(data, type, row) {
                const roleColors = {
                    'Admin': 'bg-danger',
                    'Accountant': 'bg-primary',
                    'Branch Employee': 'bg-success',
                    'Viewer': 'bg-info',
                    'Courier': 'bg-warning'
                };
                const colorClass = roleColors[data] || 'bg-secondary';
                return `<span class="badge ${colorClass}">${data}</span>`;
            }
        },
        {
            data: 'location',
            name: 'location'
        },
        {
            data: 'is_active',
            name: 'is_active',
            render: function(data, type, row) {
                if (row.is_locked) {
                    return '<span class="badge bg-danger">{% trans "Locked" %}</span>';
                }
                const badgeClass = data ? 'bg-success' : 'bg-secondary';
                const text = data ? '{% trans "Active" %}' : '{% trans "Inactive" %}';
                return `<span class="badge ${badgeClass}">${text}</span>`;
            }
        },
        {
            data: 'last_login',
            name: 'last_login'
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                let actions = `
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewUser('${row.id}')" title="{% trans 'View Details' %}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="editUser('${row.id}')" title="{% trans 'Edit' %}">
                            <i class="bi bi-pencil"></i>
                        </button>
                `;

                if (row.is_locked) {
                    actions += `
                        <button class="btn btn-sm btn-outline-success" onclick="unlockUser('${row.id}')" title="{% trans 'Unlock Account' %}">
                            <i class="bi bi-unlock"></i>
                        </button>
                    `;
                }

                if (row.is_active) {
                    actions += `
                        <button class="btn btn-sm btn-outline-warning" onclick="deactivateUser('${row.id}')" title="{% trans 'Deactivate' %}">
                            <i class="bi bi-pause"></i>
                        </button>
                    `;
                } else {
                    actions += `
                        <button class="btn btn-sm btn-outline-success" onclick="activateUser('${row.id}')" title="{% trans 'Activate' %}">
                            <i class="bi bi-play"></i>
                        </button>
                    `;
                }

                actions += `</div>`;
                return actions;
            }
        }
    ];

    // Initialize DataTable with proper configuration for user management
    usersTable = $('#users-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/accounts/api/user-list/',
            type: 'GET',
            headers: getAuthHeaders(),
            data: function(d) {
                // Add filter parameters
                const formData = new FormData($('#filter-form')[0]);
                const filters = {};

                for (let [key, value] of formData.entries()) {
                    if (value) {
                        filters[key] = value;
                    }
                }

                console.log('🔍 User management DataTables request:', Object.assign(d, filters));
                return Object.assign(d, filters);
            },
            error: function(xhr, error, thrown) {
                console.error('❌ User management DataTables AJAX error:', error, thrown);
                console.error('Request details:', {
                    url: xhr.responseURL || '/users/api/user-list/',
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText
                });
                showAlert('error', 'Failed to load users: ' + (xhr.responseJSON?.error || error));
            },
            dataSrc: function(json) {
                console.log('✅ User management DataTables response:', json);
                json.recordsTotal = json.recordsTotal || json.count;
                json.recordsFiltered = json.recordsFiltered || json.count;
                return json.data || json.results;
            }
        },
        columns: columns,
        order: [[1, 'asc']], // Order by name
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        responsive: true,
        language: {
            search: 'Search:',
            lengthMenu: 'Show _MENU_ entries',
            info: 'Showing _START_ to _END_ of _TOTAL_ entries',
            infoEmpty: 'Showing 0 to 0 of 0 entries',
            infoFiltered: '(filtered from _MAX_ total entries)',
            loadingRecords: 'Loading...',
            processing: 'Processing...',
            zeroRecords: 'No matching records found',
            paginate: {
                first: 'First',
                last: 'Last',
                next: 'Next',
                previous: 'Previous'
            }
        }
    });
}

function showUserModal(userId = null) {
    isEditMode = !!userId;

    // Reset form
    $('#user-form')[0].reset();
    $('#user-id').val(userId || '');

    // Update modal title and password requirements
    if (isEditMode) {
        $('#userModalTitle').text('{% trans "Edit User" %}');
        $('#password-required, #password-confirm-required').hide();
        $('#password, #password-confirm').removeAttr('required');
    } else {
        $('#userModalTitle').text('{% trans "New User" %}');
        $('#password-required, #password-confirm-required').show();
        $('#password, #password-confirm').attr('required', true);
    }

    // Load user data if editing
    if (isEditMode) {
        loadUserData(userId);
    }

    $('#userModal').modal('show');
}

function loadUserData(userId) {
    $.ajax({
        url: `/api/v1/accounts/users/${userId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(user) {
            $('#username').val(user.username);
            $('#email').val(user.email);
            $('#first-name').val(user.first_name);
            $('#last-name').val(user.last_name);
            $('#phone-number').val(user.phone_number);
            $('#employee-id').val(user.employee_id);
            $('#role').val(user.role);
            $('#location').val(user.location);
            $('#is-active').prop('checked', user.is_active);
            $('#department').val(user.department);
            $('#notes').val(user.notes);
        },
        error: function(xhr) {
            console.error('Error loading user data:', xhr);
            showAlert('danger', '{% trans "Error loading user data" %}');
        }
    });
}

function saveUser() {
    console.log('saveUser() called');
    const formData = new FormData($('#user-form')[0]);
    const userId = $('#user-id').val();

    console.log('Form data entries:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    // Validate form
    if (!validateUserForm()) {
        console.log('Form validation failed');
        return;
    }

    console.log('Form validation passed');

    // Convert FormData to JSON
    const userData = {};
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active') {
            userData[key] = $('#is-active').is(':checked');
        } else if (value) {
            userData[key] = value;
        }
    }

    console.log('User data to send:', userData);

    const url = isEditMode ? `/api/v1/accounts/users/${userId}/` : '/api/v1/accounts/users/';
    const method = isEditMode ? 'PUT' : 'POST';

    console.log('Making AJAX request:', { url, method, userData });

    $.ajax({
        url: url,
        method: method,
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify(userData),
        beforeSend: function(xhr, settings) {
            console.log('Request headers:', settings.headers);
            console.log('Request data:', settings.data);
        },
        success: function(response) {
            console.log('User save successful:', response);
            $('#userModal').modal('hide');
            usersTable.ajax.reload();
            loadUserStats();

            const message = isEditMode ?
                '{% trans "User updated successfully" %}' :
                '{% trans "User created successfully" %}';
            showAlert('success', message);
        },
        error: function(xhr) {
            console.error('Error saving user:', xhr);
            console.error('Response status:', xhr.status);
            console.error('Response text:', xhr.responseText);
            console.error('Response JSON:', xhr.responseJSON);

            let errorMessage = '{% trans "Error saving user" %}';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join(', ');
                } else if (xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                } else if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
            }

            showAlert('danger', errorMessage);
        }
    });
}

function validateUserForm() {
    let isValid = true;

    // Clear previous validation
    $('.is-invalid').removeClass('is-invalid');

    // Required fields
    const requiredFields = ['username', 'email', 'first-name', 'last-name', 'role'];
    requiredFields.forEach(fieldId => {
        const field = $(`#${fieldId}`);
        if (!field.val().trim()) {
            field.addClass('is-invalid');
            isValid = false;
        }
    });

    // Password validation for new users
    if (!isEditMode) {
        const password = $('#password').val();
        const confirmPassword = $('#password-confirm').val();

        if (!password) {
            $('#password').addClass('is-invalid');
            isValid = false;
        } else if (password.length < 8) {
            $('#password').addClass('is-invalid');
            showAlert('warning', '{% trans "Password must be at least 8 characters long" %}');
            isValid = false;
        }

        if (password !== confirmPassword) {
            $('#password-confirm').addClass('is-invalid');
            showAlert('warning', '{% trans "Passwords do not match" %}');
            isValid = false;
        }
    }

    // Email validation
    const email = $('#email').val();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
        $('#email').addClass('is-invalid');
        showAlert('warning', '{% trans "Please enter a valid email address" %}');
        isValid = false;
    }

    return isValid;
}

function viewUser(userId) {
    window.location.href = `/users/${userId}/`;
}

function editUser(userId) {
    showUserModal(userId);
}

function unlockUser(userId) {
    if (!confirm('{% trans "Are you sure you want to unlock this user account?" %}')) {
        return;
    }

    $.ajax({
        url: `/accounts/api/users/${userId}/unlock/`,
        method: 'POST',
        headers: getAuthHeaders(),
        success: function(response) {
            usersTable.ajax.reload();
            loadUserStats();
            showAlert('success', response.message);
        },
        error: function(xhr) {
            console.error('Error unlocking user:', xhr);
            showAlert('danger', '{% trans "Error unlocking user account" %}');
        }
    });
}

function activateUser(userId) {
    updateUserStatus(userId, true, '{% trans "activate" %}');
}

function deactivateUser(userId) {
    updateUserStatus(userId, false, '{% trans "deactivate" %}');
}

function updateUserStatus(userId, isActive, action) {
    if (!confirm(`{% trans "Are you sure you want to" %} ${action} {% trans "this user?" %}`)) {
        return;
    }

    $.ajax({
        url: `/api/v1/accounts/users/${userId}/`,
        method: 'PATCH',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify({ is_active: isActive }),
        success: function(response) {
            usersTable.ajax.reload();
            loadUserStats();

            const message = isActive ?
                '{% trans "User activated successfully" %}' :
                '{% trans "User deactivated successfully" %}';
            showAlert('success', message);
        },
        error: function(xhr) {
            console.error('Error updating user status:', xhr);
            showAlert('danger', '{% trans "Error updating user status" %}');
        }
    });
}

function exportUsers() {
    const filters = {};
    const formData = new FormData($('#filter-form')[0]);

    for (let [key, value] of formData.entries()) {
        if (value) {
            filters[key] = value;
        }
    }

    const queryString = new URLSearchParams(filters).toString();
    const exportUrl = `/api/v1/accounts/users/export/?${queryString}`;

    // Create temporary download link
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'users_export.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the page
    $('main .container-fluid').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken();
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    // Safe CSRF token retrieval with multiple fallbacks
    const csrfToken = getCSRFToken();
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function getCSRFToken() {
    // Method 1: Try to get from form input
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfInput && csrfInput.value) {
        return csrfInput.value;
    }

    // Method 2: Try to get from jQuery
    const jqueryCsrf = $('[name=csrfmiddlewaretoken]').val();
    if (jqueryCsrf) {
        return jqueryCsrf;
    }

    // Method 3: Try to get from meta tag
    const metaCsrf = $('meta[name=csrf-token]').attr('content');
    if (metaCsrf) {
        return metaCsrf;
    }

    // Method 4: Try ArenaDoviz utils
    if (ArenaDoviz && ArenaDoviz.utils && typeof ArenaDoviz.utils.getCSRFToken === 'function') {
        const utilsCsrf = ArenaDoviz.utils.getCSRFToken();
        if (utilsCsrf) {
            return utilsCsrf;
        }
    }

    console.warn('CSRF token not found using any method');
    return null;
}
</script>
{% endblock %}
