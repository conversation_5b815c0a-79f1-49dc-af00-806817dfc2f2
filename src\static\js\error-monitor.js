
/**
 * Arena Doviz Error Monitoring System
 * Captures and reports JavaScript errors, DOM issues, and API failures
 */

class ErrorMonitor {
    constructor() {
        this.errors = [];
        this.domErrors = [];
        this.apiErrors = [];
        this.init();
    }

    init() {
        console.log('🔧 Error Monitor DISABLED to prevent infinite loops');
        // All error monitoring functionality has been disabled
        // The error monitor was causing infinite loops that broke JavaScript functionality
        return;
    }

    setupGlobalErrorHandling() {
        // Capture JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        });
    }

    setupDOMErrorDetection() {
        // Disabled DOM override to prevent infinite loops
        // The DOM error detection was causing more problems than it solved
        console.log('DOM error detection disabled to prevent infinite loops');
        return;

        document.querySelector = function(selector) {
            const element = originalQuerySelector.call(this, selector);
            if (!element && selector.includes('#')) {
                window.errorMonitor.logDOMError({
                    type: 'querySelector returned null',
                    selector: selector,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    stack: new Error().stack
                });
            }
            return element;
        };
    }

    setupAPIErrorTracking() {
        // Override jQuery AJAX to track API errors
        if (typeof $ !== 'undefined') {
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                const originalError = options.error;
                options.error = function(xhr, status, error) {
                    window.errorMonitor.logAPIError({
                        type: 'AJAX Error',
                        url: options.url || 'unknown',
                        method: options.method || options.type || 'GET',
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error,
                        timestamp: new Date().toISOString()
                    });
                    
                    if (originalError) {
                        originalError.call(this, xhr, status, error);
                    }
                };
                return originalAjax.call(this, options);
            };
        }

        // DISABLED: Fetch API override was causing infinite loops
        // The fetch override was intercepting error reporting requests
        // which created more errors, leading to infinite loops
        console.log('Fetch API monitoring disabled to prevent infinite loops');
    }

    setupConsoleOverride() {
        // Capture console errors
        const originalError = console.error;
        console.error = function(...args) {
            // Prevent infinite loop by using original console.error for logging
            try {
                window.errorMonitor.logErrorSafe({
                    type: 'Console Error',
                    message: args.join(' '),
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    stack: new Error().stack
                });
            } catch (e) {
                // If error monitoring fails, just use original console.error
                originalError.call(console, 'Error monitor failed:', e);
            }
            originalError.apply(console, args);
        };
    }

    startPeriodicChecks() {
        // Disabled periodic checks to prevent continuous DOM errors
        // Only run initial check
        setTimeout(() => {
            this.checkForCommonIssues();
        }, 2000);
    }

    checkForCommonIssues() {
        // Check if jQuery is loaded
        if (typeof $ === 'undefined') {
            this.logError({
                type: 'Missing Dependency',
                message: 'jQuery is not loaded',
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        }

        // Check if ArenaDoviz object is available
        if (typeof ArenaDoviz === 'undefined') {
            this.logError({
                type: 'Missing Dependency',
                message: 'ArenaDoviz object is not available',
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        }

        // Check for form elements that should exist (only on form pages, not list pages)
        if (window.location.pathname.includes('/transactions/') &&
            (window.location.pathname.includes('/add/') || window.location.pathname.includes('/edit/'))) {
            const expectedElements = ['#transaction-form', '#customer'];
            expectedElements.forEach(selector => {
                if ($(selector).length === 0) {
                    this.logDOMError({
                        type: 'Missing Expected Element',
                        selector: selector,
                        timestamp: new Date().toISOString(),
                        url: window.location.href
                    });
                }
            });
        }
    }

    logError(error) {
        this.errors.push(error);
        console.group('🚨 JavaScript Error Detected');
        console.error('Type:', error.type);
        console.error('Message:', error.message);
        console.error('URL:', error.url);
        if (error.stack) console.error('Stack:', error.stack);
        console.groupEnd();

        // Server reporting disabled to prevent infinite loops
    }

    logErrorSafe(error) {
        // Safe error logging that doesn't use console.error to prevent infinite loops
        this.errors.push(error);

        // Use console.warn instead of console.error to avoid infinite loop
        console.warn('🚨 JavaScript Error Detected:', error.type, '-', error.message);

        // Server reporting disabled to prevent infinite loops
    }

    logDOMError(error) {
        this.domErrors.push(error);
        console.group('🔍 DOM Error Detected');
        console.warn('Type:', error.type);
        console.warn('Selector:', error.selector);
        console.warn('URL:', error.url);
        console.groupEnd();
    }

    logAPIError(error) {
        this.apiErrors.push(error);
        console.group('🌐 API Error Detected');
        console.warn('Type:', error.type);
        console.warn('URL:', error.url);
        console.warn('Status:', error.status);
        console.warn('Error:', error.error);
        console.groupEnd();
    }

    sendErrorToServer(error) {
        // DISABLED: Server error reporting was causing infinite loops
        // The 404 errors from /api/v1/errors/log/ were creating more errors
        // which created an infinite loop that broke all JavaScript functionality
        return;
    }

    getErrorReport() {
        return {
            errors: this.errors,
            domErrors: this.domErrors,
            apiErrors: this.apiErrors,
            totalErrors: this.errors.length + this.domErrors.length + this.apiErrors.length,
            timestamp: new Date().toISOString()
        };
    }

    clearErrors() {
        this.errors = [];
        this.domErrors = [];
        this.apiErrors = [];
        console.log('🧹 Error logs cleared');
    }
}

// Initialize error monitor as soon as possible
window.errorMonitor = new ErrorMonitor();

// Add global function to get error report
window.getErrorReport = function() {
    return window.errorMonitor.getErrorReport();
};

// Add global function to clear errors
window.clearErrors = function() {
    window.errorMonitor.clearErrors();
};
