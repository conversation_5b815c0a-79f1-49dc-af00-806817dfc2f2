#!/usr/bin/env python
"""
Arena Doviz System Diagnostic Tool
Comprehensive analysis of database state, API functionality, and system health.
"""

import os
import sys
import django
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.contrib.auth import get_user_model
from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from django.db import connection
import json

User = get_user_model()

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title):
    print(f"\n--- {title} ---")

def diagnose_database():
    """Check database connectivity and basic table structure."""
    print_header("DATABASE DIAGNOSIS")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            print("✅ Database connection: OK")
    except Exception as e:
        print(f"❌ Database connection: FAILED - {e}")
        return False
    
    # Check tables exist
    tables = [
        'accounts_user',
        'transactions_transactiontype', 
        'transactions_transaction',
        'customers_customer',
        'locations_location',
        'currencies_currency'
    ]
    
    with connection.cursor() as cursor:
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ Table {table}: {count} records")
            except Exception as e:
                print(f"❌ Table {table}: ERROR - {e}")
    
    return True

def diagnose_users():
    """Check user accounts and permissions."""
    print_header("USER ACCOUNTS DIAGNOSIS")
    
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    admin_users = User.objects.filter(is_superuser=True).count()
    
    print(f"Total users: {total_users}")
    print(f"Active users: {active_users}")
    print(f"Admin users: {admin_users}")
    
    if total_users == 0:
        print("❌ No users found! Creating superuser is required.")
        return False
    
    # Check first user details
    first_user = User.objects.first()
    print(f"\nFirst user details:")
    print(f"  Username: {first_user.username}")
    print(f"  Email: {first_user.email}")
    print(f"  Is active: {first_user.is_active}")
    print(f"  Is superuser: {first_user.is_superuser}")
    print(f"  Role: {getattr(first_user, 'role', 'No role field')}")
    
    return True

def diagnose_transaction_types():
    """Check transaction types."""
    print_header("TRANSACTION TYPES DIAGNOSIS")
    
    types = TransactionType.objects.all()
    print(f"Total transaction types: {types.count()}")
    
    if types.count() == 0:
        print("❌ No transaction types found!")
        print("Run: python manage.py create_default_transaction_types")
        return False
    
    print("\nTransaction types:")
    for t in types:
        print(f"  {t.code}: {t.name} (Active: {t.is_active})")
    
    return True

def diagnose_customers():
    """Check customers."""
    print_header("CUSTOMERS DIAGNOSIS")
    
    customers = Customer.objects.filter(is_deleted=False)
    print(f"Total customers: {customers.count()}")
    
    if customers.count() == 0:
        print("❌ No customers found!")
        print("This explains why customer dropdowns are empty.")
        return False
    
    print("\nFirst 5 customers:")
    for c in customers[:5]:
        display_name = c.display_name if hasattr(c, 'display_name') else f"{c.first_name} {c.last_name}"
        print(f"  {c.customer_code}: {display_name}")
    
    return True

def diagnose_locations():
    """Check locations."""
    print_header("LOCATIONS DIAGNOSIS")
    
    locations = Location.objects.filter(is_deleted=False)
    print(f"Total locations: {locations.count()}")
    
    if locations.count() == 0:
        print("❌ No locations found!")
        return False
    
    for loc in locations:
        print(f"  {loc.code}: {loc.name}")
    
    return True

def diagnose_currencies():
    """Check currencies."""
    print_header("CURRENCIES DIAGNOSIS")
    
    currencies = Currency.objects.filter(is_deleted=False)
    print(f"Total currencies: {currencies.count()}")
    
    if currencies.count() == 0:
        print("❌ No currencies found!")
        return False
    
    for curr in currencies:
        print(f"  {curr.code}: {curr.name} (Active: {curr.is_active})")
    
    return True

def diagnose_transactions():
    """Check transactions."""
    print_header("TRANSACTIONS DIAGNOSIS")
    
    transactions = Transaction.objects.filter(is_deleted=False)
    print(f"Total transactions: {transactions.count()}")
    
    if transactions.count() == 0:
        print("❌ No transactions found!")
        print("This explains why transaction lists are empty.")
        return False
    
    # Group by transaction type
    print("\nTransactions by type:")
    for t_type in TransactionType.objects.all():
        count = transactions.filter(transaction_type=t_type).count()
        print(f"  {t_type.code}: {count} transactions")
    
    return True

def main():
    """Run comprehensive diagnosis."""
    print_header("ARENA DOVIZ SYSTEM DIAGNOSIS")
    print("Analyzing system state to identify root causes...")
    
    results = {
        'database': diagnose_database(),
        'users': diagnose_users(),
        'transaction_types': diagnose_transaction_types(),
        'customers': diagnose_customers(),
        'locations': diagnose_locations(),
        'currencies': diagnose_currencies(),
        'transactions': diagnose_transactions(),
    }
    
    print_header("DIAGNOSIS SUMMARY")
    
    failed_checks = []
    for check, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check.upper()}: {status}")
        if not result:
            failed_checks.append(check)
    
    if failed_checks:
        print(f"\n❌ CRITICAL ISSUES FOUND: {', '.join(failed_checks)}")
        print("\nRECOMMENDED ACTIONS:")
        
        if 'transaction_types' in failed_checks:
            print("1. Run: python manage.py create_default_transaction_types")
        
        if 'customers' in failed_checks:
            print("2. Create sample customers or run seed data command")
        
        if 'locations' in failed_checks:
            print("3. Create locations (Istanbul, Dubai, etc.)")
        
        if 'currencies' in failed_checks:
            print("4. Create currencies (USD, AED, IRR)")
        
        if 'transactions' in failed_checks:
            print("5. Create sample transactions for testing")
    else:
        print("\n✅ ALL CHECKS PASSED - Issue may be in frontend/API layer")

if __name__ == '__main__':
    main()
