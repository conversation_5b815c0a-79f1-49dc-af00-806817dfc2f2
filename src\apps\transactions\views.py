"""
API views for Arena Doviz Transactions app.
Handles transaction processing, balance management, and related operations.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Sum, Q
from django.db import transaction
from django.utils import timezone
from datetime import <PERSON>el<PERSON>
from .models import TransactionType, Transaction, BalanceEntry, TransactionDocument
from .commission_models import CommissionRule, CommissionTier
from .commission_utils import commission_calculator, commission_rule_manager
from .serializers import (
    TransactionTypeSerializer, TransactionSerializer, TransactionListSerializer,
    TransactionCreateSerializer, TransactionUpdateSerializer, TransactionStatusSerializer,
    BalanceEntrySerializer, TransactionStatsSerializer, BalanceSummarySerializer,
    CustomerBalanceSerializer, CompanyBalanceSerializer, BalanceCalculationSerializer,
    TransactionDocumentSerializer, TransactionDocumentUploadSerializer,
    CommissionRuleSerializer, CommissionRuleListSerializer, CommissionTierSerializer,
    CommissionCalculationSerializer, CommissionCalculationResultSerializer
)
from apps.core.utils import log_user_action, get_client_ip
from django.utils.dateformat import format as date_format
import logging

logger = logging.getLogger(__name__)


class TransactionTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for TransactionType management."""
    
    queryset = TransactionType.objects.filter(is_deleted=False)
    serializer_class = TransactionTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['code']
    
    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = self.queryset
        
        # Filter by active status if requested
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)
        
        return queryset


class TransactionViewSet(viewsets.ModelViewSet):
    """ViewSet for Transaction management."""
    
    queryset = Transaction.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['transaction_number', 'description', 'reference_number', 'customer__first_name', 'customer__last_name']
    ordering_fields = ['transaction_number', 'created_at', 'from_amount', 'status']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return TransactionListSerializer
        elif self.action == 'create':
            return TransactionCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return TransactionUpdateSerializer
        elif self.action in ['approve', 'complete', 'cancel', 'reject']:
            return TransactionStatusSerializer
        return TransactionSerializer
    
    def get_queryset(self):
        """Filter queryset based on user permissions and query parameters."""
        user = self.request.user
        queryset = self.queryset

        # Filter by user's location if not admin
        if not user.can_manage_users() and user.location:
            queryset = queryset.filter(location=user.location)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by customer
        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        # Filter by location
        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)

        # Filter by currency
        currency_code = self.request.query_params.get('currency')
        if currency_code:
            queryset = queryset.filter(
                Q(from_currency__code=currency_code) | Q(to_currency__code=currency_code)
            )

        # Filter by transaction type
        transaction_type_id = self.request.query_params.get('transaction_type')
        if transaction_type_id:
            queryset = queryset.filter(transaction_type_id=transaction_type_id)

        # Filter by transaction type code
        transaction_type_code = self.request.query_params.get('transaction_type_code')
        if transaction_type_code:
            logger.info(f"Filtering transactions by type code: {transaction_type_code}")
            queryset = queryset.filter(transaction_type__code=transaction_type_code)
            logger.info(f"Filtered queryset count: {queryset.count()}")

        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset

    def perform_update(self, serializer):
        """Update transaction with permission checking."""
        # Check if user can manage transactions
        if not self.request.user.can_manage_transactions():
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to update transactions.")

        # Save the transaction (logging is handled by the serializer)
        serializer.save()
    
    def perform_create(self, serializer):
        """Create transaction with permission checking and audit logging."""
        logger.info(f"Transaction creation attempt by {self.request.user}: {self.request.data}")

        # Check if user can manage transactions
        if not self.request.user.can_manage_transactions():
            from rest_framework.exceptions import PermissionDenied
            logger.warning(f"Permission denied for transaction creation: {self.request.user}")
            raise PermissionDenied("You don't have permission to create transactions.")

        # Set the created_by user and location if not provided
        validated_data = serializer.validated_data.copy()

        # Set location to user's location if not provided
        if not validated_data.get('location') and self.request.user.location:
            validated_data['location'] = self.request.user.location
            logger.info(f"Setting location to user's location: {self.request.user.location}")

        # Save the transaction
        transaction_obj = serializer.save(created_by=self.request.user, **validated_data)
        logger.info(f"Transaction created successfully: {transaction_obj.transaction_number}")

    def create(self, request, *args, **kwargs):
        """Create a new transaction with enhanced error handling and retry logic."""
        import time
        from django.db import transaction as db_transaction
        from django.db.utils import OperationalError

        max_retries = 3
        retry_delay = 0.5

        for attempt in range(max_retries):
            try:
                logger.info(f"Transaction creation request from {request.user} (attempt {attempt + 1}/{max_retries}): {request.data}")

                # Validate required fields before serialization
                required_fields = ['customer', 'from_currency', 'to_currency', 'from_amount', 'to_amount']
                missing_fields = []

                for field in required_fields:
                    if field not in request.data or not request.data[field]:
                        missing_fields.append(field)

                if missing_fields:
                    logger.warning(f"Missing required fields: {missing_fields}")
                    return Response({
                        'error': 'Missing required fields',
                        'missing_fields': missing_fields,
                        'details': f"The following fields are required: {', '.join(missing_fields)}"
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Use atomic transaction with retry logic
                with db_transaction.atomic():
                    # Call the parent create method
                    response = super().create(request, *args, **kwargs)
                    logger.info(f"Transaction created successfully on attempt {attempt + 1}")
                    return response

            except OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    logger.warning(f"Database locked during transaction creation, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    logger.error(f"Database lock persisted after {max_retries} attempts: {str(e)}")
                    return Response({
                        'error': 'Transaction creation failed',
                        'message': 'Database is temporarily unavailable. Please try again in a moment.',
                        'technical_details': str(e),
                        'request_data': request.data
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            except Exception as e:
                logger.error(f"Transaction creation failed on attempt {attempt + 1}: {str(e)}", exc_info=True)

                # Don't retry for validation errors or permission errors
                if hasattr(e, 'detail') or "permission" in str(e).lower():
                    break

                # Retry for other errors
                if attempt < max_retries - 1:
                    logger.warning(f"Retrying transaction creation in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    break

        # Final error response after all retries failed
        error_response = {
            'error': 'Transaction creation failed',
            'message': str(e),
            'request_data': request.data
        }

        # Add validation errors if available
        if hasattr(e, 'detail'):
            error_response['validation_errors'] = e.detail

        return Response(error_response, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a pending transaction."""
        transaction_obj = self.get_object()

        logger.info(f"Transaction approval attempt: {transaction_obj.transaction_number} (Status: {transaction_obj.status}) by {request.user}")

        # Check if user can approve transactions
        if not request.user.can_approve_transactions():
            logger.warning(f"Permission denied for transaction approval: {request.user} cannot approve transactions")
            return Response(
                {'error': 'You do not have permission to approve transactions'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if transaction can be approved
        if not transaction_obj.can_be_approved():
            logger.warning(f"Transaction {transaction_obj.transaction_number} cannot be approved - current status: {transaction_obj.status}")
            return Response(
                {
                    'error': f'Transaction cannot be approved in its current state. Current status: {transaction_obj.get_status_display()}',
                    'current_status': transaction_obj.status,
                    'allowed_statuses': ['draft', 'pending']
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Use TransactionStatusSerializer for status changes
        from .serializers import TransactionStatusSerializer
        serializer = TransactionStatusSerializer(transaction_obj, data={'status': Transaction.Status.APPROVED})
        if serializer.is_valid():
            with transaction.atomic():
                transaction_obj.status = Transaction.Status.APPROVED
                transaction_obj.approved_by = request.user
                transaction_obj.approved_at = timezone.now()
                transaction_obj.save()
                
                # Log the approval
                log_user_action(
                    user=request.user,
                    action='approve_transaction',
                    model_name='Transaction',
                    object_id=str(transaction_obj.id),
                    object_repr=str(transaction_obj),
                    ip_address=get_client_ip(request),
                    additional_data={
                        'transaction_number': transaction_obj.transaction_number,
                        'customer': transaction_obj.customer.get_display_name(),
                        'amount': str(transaction_obj.from_amount)
                    }
                )
                
                logger.info(f"Transaction approved: {transaction_obj.transaction_number} by {request.user}")
                
                return Response({
                    'message': 'Transaction approved successfully',
                    'transaction': TransactionSerializer(transaction_obj).data
                })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Complete an approved transaction."""
        transaction_obj = self.get_object()
        
        # Check if user can complete transactions
        if not request.user.can_complete_transactions():
            return Response(
                {'error': 'You do not have permission to complete transactions'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if transaction can be completed
        if transaction_obj.status != Transaction.Status.APPROVED:
            return Response(
                {'error': 'Only approved transactions can be completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            transaction_obj.status = Transaction.Status.COMPLETED
            transaction_obj.completed_at = timezone.now()
            transaction_obj.save()
            
            # Log the completion
            log_user_action(
                user=request.user,
                action='complete_transaction',
                model_name='Transaction',
                object_id=str(transaction_obj.id),
                object_repr=str(transaction_obj),
                ip_address=get_client_ip(request),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'customer': transaction_obj.customer.get_display_name(),
                    'amount': str(transaction_obj.from_amount)
                }
            )
            
            logger.info(f"Transaction completed: {transaction_obj.transaction_number} by {request.user}")
            
            return Response({
                'message': 'Transaction completed successfully',
                'transaction': TransactionSerializer(transaction_obj).data
            })
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a transaction."""
        transaction_obj = self.get_object()
        
        # Check if transaction can be cancelled
        if not transaction_obj.can_be_cancelled():
            return Response(
                {'error': 'Transaction cannot be cancelled in its current state'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        notes = request.data.get('notes', '')
        
        with transaction.atomic():
            transaction_obj.status = Transaction.Status.CANCELLED
            if notes:
                transaction_obj.notes = f"{transaction_obj.notes}\n\nCancellation notes: {notes}".strip()
            transaction_obj.save()
            
            # Log the cancellation
            log_user_action(
                user=request.user,
                action='cancel_transaction',
                model_name='Transaction',
                object_id=str(transaction_obj.id),
                object_repr=str(transaction_obj),
                ip_address=get_client_ip(request),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'customer': transaction_obj.customer.get_display_name(),
                    'cancellation_notes': notes
                }
            )
            
            logger.info(f"Transaction cancelled: {transaction_obj.transaction_number} by {request.user}")
            
            return Response({
                'message': 'Transaction cancelled successfully',
                'transaction': TransactionSerializer(transaction_obj).data
            })
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a pending transaction."""
        transaction_obj = self.get_object()
        
        # Check if user can reject transactions
        if not request.user.can_approve_transactions():
            return Response(
                {'error': 'You do not have permission to reject transactions'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if transaction can be rejected
        if transaction_obj.status != Transaction.Status.PENDING:
            return Response(
                {'error': 'Only pending transactions can be rejected'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        notes = request.data.get('notes', '')
        
        with transaction.atomic():
            transaction_obj.status = Transaction.Status.REJECTED
            if notes:
                transaction_obj.notes = f"{transaction_obj.notes}\n\nRejection notes: {notes}".strip()
            transaction_obj.save()
            
            # Log the rejection
            log_user_action(
                user=request.user,
                action='reject_transaction',
                resource_type='Transaction',
                resource_id=str(transaction_obj.id),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'customer': transaction_obj.customer.get_display_name(),
                    'rejection_notes': notes
                }
            )
            
            logger.info(f"Transaction rejected: {transaction_obj.transaction_number} by {request.user}")
            
            return Response({
                'message': 'Transaction rejected successfully',
                'transaction': TransactionSerializer(transaction_obj).data
            })

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get transaction statistics."""
        user = request.user
        queryset = self.get_queryset()

        # Calculate basic statistics
        total_transactions = queryset.count()
        pending_transactions = queryset.filter(status=Transaction.Status.PENDING).count()
        completed_transactions = queryset.filter(status=Transaction.Status.COMPLETED).count()
        cancelled_transactions = queryset.filter(status=Transaction.Status.CANCELLED).count()

        # Transactions by status
        transactions_by_status = dict(
            queryset.values('status')
            .annotate(count=Count('id'))
            .values_list('status', 'count')
        )

        # Transactions by location
        transactions_by_location = dict(
            queryset.values('location__name')
            .annotate(count=Count('id'))
            .values_list('location__name', 'count')
        )

        # Transactions by currency
        transactions_by_currency = dict(
            queryset.values('from_currency__code')
            .annotate(count=Count('id'))
            .values_list('from_currency__code', 'count')
        )

        # Today's statistics
        today = timezone.now().date()
        today_transactions = queryset.filter(created_at__date=today)

        total_volume_today = today_transactions.filter(
            status=Transaction.Status.COMPLETED
        ).aggregate(
            total=Sum('from_amount')
        )['total'] or 0

        total_commission_today = today_transactions.filter(
            status=Transaction.Status.COMPLETED
        ).aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        # Average transaction amount
        avg_amount = queryset.filter(
            status=Transaction.Status.COMPLETED
        ).aggregate(
            avg=Sum('from_amount')
        )['avg'] or 0

        if completed_transactions > 0:
            average_transaction_amount = avg_amount / completed_transactions
        else:
            average_transaction_amount = 0

        stats_data = {
            'total_transactions': total_transactions,
            'pending_transactions': pending_transactions,
            'completed_transactions': completed_transactions,
            'cancelled_transactions': cancelled_transactions,
            'transactions_by_status': transactions_by_status,
            'transactions_by_location': transactions_by_location,
            'transactions_by_currency': transactions_by_currency,
            'total_volume_today': total_volume_today,
            'total_commission_today': total_commission_today,
            'average_transaction_amount': average_transaction_amount
        }

        serializer = TransactionStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export transactions to CSV format."""
        try:
            from django.http import HttpResponse
            import csv
            from datetime import datetime

            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            # Get format parameter
            export_format = request.query_params.get('format', 'csv').lower()

            if export_format not in ['csv', 'excel']:
                return Response(
                    {'error': 'Invalid format. Supported formats: csv, excel'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create response
            if export_format == 'csv':
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="transactions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

                writer = csv.writer(response)

                # Write header
                writer.writerow([
                    'Transaction Number',
                    'Date',
                    'Customer',
                    'Location',
                    'Type',
                    'From Currency',
                    'To Currency',
                    'From Amount',
                    'To Amount',
                    'Exchange Rate',
                    'Commission Amount',
                    'Status',
                    'Created By'
                ])

                # Write data
                for transaction in queryset:
                    writer.writerow([
                        transaction.transaction_number,
                        transaction.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                        transaction.customer.get_display_name() if transaction.customer else 'N/A',
                        transaction.location.name if transaction.location else 'N/A',
                        transaction.transaction_type.name if transaction.transaction_type else 'N/A',
                        transaction.from_currency.code if transaction.from_currency else 'N/A',
                        transaction.to_currency.code if transaction.to_currency else 'N/A',
                        str(transaction.from_amount),
                        str(transaction.to_amount),
                        str(transaction.exchange_rate),
                        str(transaction.commission_amount or 0),
                        transaction.get_status_display(),
                        transaction.created_by.username if transaction.created_by else 'N/A'
                    ])

                logger.info(f"Transactions exported by user: {request.user.username}")
                return response

            else:  # excel format
                return Response(
                    {'error': 'Excel export not yet implemented'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )

        except Exception as e:
            logger.error(f"Error exporting transactions: {str(e)}")
            return Response(
                {'error': 'Failed to export transactions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'], url_path='whatsapp-message')
    def generate_whatsapp_message(self, request, pk=None):
        """Generate WhatsApp message for a transaction."""
        try:
            transaction_obj = self.get_object()

            # Check if customer has WhatsApp group ID
            if not transaction_obj.customer.whatsapp_group_id:
                return Response(
                    {'error': 'Customer does not have a WhatsApp group ID configured'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Generate message based on transaction type
            message = self._generate_transaction_message(transaction_obj)

            # Log the message generation
            log_user_action(
                user=request.user,
                action='generate_whatsapp_message',
                model_name='Transaction',
                object_id=str(transaction_obj.id),
                object_repr=str(transaction_obj),
                ip_address=get_client_ip(request),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'customer': transaction_obj.customer.get_display_name(),
                    'whatsapp_group_id': transaction_obj.customer.whatsapp_group_id
                }
            )

            return Response({
                'message': message,
                'whatsapp_group_id': transaction_obj.customer.whatsapp_group_id,
                'customer_name': transaction_obj.customer.get_display_name(),
                'transaction_number': transaction_obj.transaction_number
            })

        except Exception as e:
            logger.error(f"Error generating WhatsApp message for transaction {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to generate WhatsApp message'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_transaction_message(self, transaction_obj):
        """Generate formatted WhatsApp message based on transaction type."""
        customer_name = transaction_obj.customer.get_display_name()
        transaction_date = date_format(transaction_obj.created_at, 'Y-m-d H:i')
        transaction_type = transaction_obj.transaction_type.name

        # Base message template
        if transaction_type.upper() in ['EXCHANGE', 'BUY', 'SELL']:
            # Currency exchange message
            message = (
                f"Dear {customer_name},\n\n"
                f"Your currency exchange of {transaction_obj.from_currency.format_amount_with_symbol(transaction_obj.from_amount)} "
                f"to {transaction_obj.to_currency.format_amount_with_symbol(transaction_obj.to_amount)} has been processed.\n\n"
                f"Reference: {transaction_obj.transaction_number}\n"
                f"Date: {transaction_date}\n"
                f"Status: {transaction_obj.get_status_display()}\n\n"
                f"Thank you for choosing Arena Doviz."
            )
        elif transaction_type.upper() in ['DEPOSIT', 'WITHDRAWAL']:
            # Cash deposit/withdrawal message
            action = "deposit" if transaction_type.upper() == 'DEPOSIT' else "withdrawal"
            message = (
                f"Dear {customer_name},\n\n"
                f"Your {action} of {transaction_obj.from_currency.format_amount_with_symbol(transaction_obj.from_amount)} "
                f"has been processed.\n\n"
                f"Reference: {transaction_obj.transaction_number}\n"
                f"Date: {transaction_date}\n"
                f"Status: {transaction_obj.get_status_display()}\n\n"
                f"Thank you for choosing Arena Doviz."
            )
        elif transaction_type.upper() in ['TRANSFER', 'INTERNAL_TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNATIONAL_TRANSFER']:
            # Money transfer message
            message = (
                f"Dear {customer_name},\n\n"
                f"Your money transfer of {transaction_obj.from_currency.format_amount_with_symbol(transaction_obj.from_amount)} "
                f"has been processed.\n\n"
                f"Reference: {transaction_obj.transaction_number}\n"
                f"Date: {transaction_date}\n"
                f"Status: {transaction_obj.get_status_display()}\n\n"
                f"Thank you for choosing Arena Doviz."
            )
        elif transaction_type.upper() == 'REMITTANCE':
            # Remittance message
            message = (
                f"Dear {customer_name},\n\n"
                f"Your remittance of {transaction_obj.from_currency.format_amount_with_symbol(transaction_obj.from_amount)} "
                f"has been processed.\n\n"
                f"Reference: {transaction_obj.transaction_number}\n"
                f"Date: {transaction_date}\n"
                f"Status: {transaction_obj.get_status_display()}\n\n"
                f"Thank you for choosing Arena Doviz."
            )
        else:
            # Generic transaction message
            message = (
                f"Dear {customer_name},\n\n"
                f"Your {transaction_type.lower()} transaction of {transaction_obj.from_currency.format_amount_with_symbol(transaction_obj.from_amount)} "
                f"has been processed.\n\n"
                f"Reference: {transaction_obj.transaction_number}\n"
                f"Date: {transaction_date}\n"
                f"Status: {transaction_obj.get_status_display()}\n\n"
                f"Thank you for choosing Arena Doviz."
            )

        return message

    @action(detail=False, methods=['get'])
    def calculate_fee(self, request):
        """Calculate service fee for a transaction."""
        transaction_type = request.query_params.get('transaction_type')
        amount = request.query_params.get('amount')
        currency_id = request.query_params.get('currency')
        location_id = request.query_params.get('location')

        if not transaction_type or not amount or not currency_id:
            return Response(
                {'error': 'transaction_type, amount, and currency parameters are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from apps.currencies.models import Currency
            from apps.locations.models import Location
            from decimal import Decimal

            # Get currency
            currency = Currency.objects.get(id=currency_id, is_active=True)

            # Get location if provided
            location = None
            if location_id:
                location = Location.objects.get(id=location_id, is_active=True)

            # Convert amount to Decimal
            amount_decimal = Decimal(str(amount))

            # Simple fee calculation based on transaction type
            fee_percentage = 0
            fixed_fee = Decimal('0')

            # Define fee structure
            fee_structure = {
                'EXCHANGE': {'percentage': 0.5, 'fixed': 0},
                'DEPOSIT': {'percentage': 0.2, 'fixed': 1},
                'WITHDRAWAL': {'percentage': 0.2, 'fixed': 1},
                'INTERNAL_TRANSFER': {'percentage': 0.1, 'fixed': 0.5},
                'EXTERNAL_TRANSFER': {'percentage': 0.3, 'fixed': 2},
                'INTERNATIONAL_TRANSFER': {'percentage': 1.0, 'fixed': 5},
                'REMITTANCE': {'percentage': 0.8, 'fixed': 3},
            }

            # Get fee structure for transaction type
            if transaction_type in fee_structure:
                fee_config = fee_structure[transaction_type]
                fee_percentage = fee_config['percentage']
                fixed_fee = Decimal(str(fee_config['fixed']))

            # Calculate fee
            percentage_fee = amount_decimal * (Decimal(str(fee_percentage)) / 100)
            total_fee = percentage_fee + fixed_fee

            return Response({
                'fee': float(total_fee),
                'percentage': fee_percentage,
                'fixed_fee': float(fixed_fee),
                'percentage_fee': float(percentage_fee),
                'description': f'{transaction_type.replace("_", " ").title()} Fee',
                'currency': currency.code,
                'calculation': {
                    'amount': float(amount_decimal),
                    'percentage_rate': fee_percentage,
                    'fixed_amount': float(fixed_fee),
                    'total_fee': float(total_fee)
                }
            })

        except Currency.DoesNotExist:
            return Response(
                {'error': 'Currency not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Location.DoesNotExist:
            return Response(
                {'error': 'Location not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except (ValueError, TypeError) as e:
            return Response(
                {'error': f'Invalid amount: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error calculating fee: {str(e)}")
            return Response(
                {'error': 'Failed to calculate fee'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BalanceEntryViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for BalanceEntry management (read-only)."""

    queryset = BalanceEntry.objects.filter(is_deleted=False)
    serializer_class = BalanceEntrySerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter queryset based on user permissions and query parameters."""
        user = self.request.user
        queryset = self.queryset

        # Filter by user's location if not admin
        if not user.can_manage_users() and user.location:
            queryset = queryset.filter(location=user.location)

        # Filter by customer
        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        # Filter by location
        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)

        # Filter by currency
        currency_code = self.request.query_params.get('currency')
        if currency_code:
            queryset = queryset.filter(currency__code=currency_code)

        # Filter by transaction
        transaction_id = self.request.query_params.get('transaction')
        if transaction_id:
            queryset = queryset.filter(transaction_id=transaction_id)

        # Filter by entry type
        entry_type = self.request.query_params.get('entry_type')
        if entry_type:
            queryset = queryset.filter(entry_type=entry_type)

        return queryset

    @action(detail=False, methods=['get'])
    def balances(self, request):
        """Get current balances by customer, location, and currency."""
        user = request.user

        # Get query parameters
        customer_id = request.query_params.get('customer')
        location_id = request.query_params.get('location')
        currency_code = request.query_params.get('currency')

        # Base queryset
        queryset = self.get_queryset()

        # Apply filters
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        if location_id:
            queryset = queryset.filter(location_id=location_id)
        if currency_code:
            queryset = queryset.filter(currency__code=currency_code)

        # Calculate current balances
        balances = {}

        # Group by customer, location, and currency
        for entry in queryset.order_by('customer', 'location', 'currency', '-created_at'):
            key = f"{entry.customer_id or 'company'}_{entry.location_id}_{entry.currency_id}"

            if key not in balances:
                balances[key] = {
                    'customer': entry.customer.get_display_name() if entry.customer else 'Company',
                    'customer_id': entry.customer_id,
                    'location': entry.location.name,
                    'location_id': entry.location_id,
                    'currency': entry.currency.code,
                    'currency_id': entry.currency_id,
                    'currency_symbol': entry.currency.symbol,
                    'balance': entry.running_balance or 0,
                    'formatted_balance': entry.currency.format_amount_with_symbol(entry.running_balance or 0),
                    'last_updated': entry.created_at
                }

        return Response(list(balances.values()))


class TransactionDocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for TransactionDocument management."""

    queryset = TransactionDocument.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return TransactionDocumentUploadSerializer
        return TransactionDocumentSerializer

    def get_queryset(self):
        """Filter documents based on user permissions and query parameters."""
        user = self.request.user
        queryset = self.queryset

        # Filter by user's location if not admin
        if not user.can_manage_users() and user.location:
            queryset = queryset.filter(transaction__location=user.location)

        # Filter by transaction
        transaction_id = self.request.query_params.get('transaction')
        if transaction_id:
            queryset = queryset.filter(transaction_id=transaction_id)

        # Filter by document type
        document_type = self.request.query_params.get('document_type')
        if document_type:
            queryset = queryset.filter(document_type=document_type)

        # Filter by verification status
        is_verified = self.request.query_params.get('is_verified')
        if is_verified is not None:
            queryset = queryset.filter(is_verified=is_verified.lower() == 'true')

        return queryset

    def perform_create(self, serializer):
        """Create document with proper user assignment."""
        serializer.save(uploaded_by=self.request.user)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify a transaction document."""
        document = self.get_object()

        # Check if user can verify documents
        if not request.user.can_approve_transactions():
            return Response(
                {'error': 'You do not have permission to verify documents'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not document.is_verified:
            document.is_verified = True
            document.verified_by = request.user
            document.verified_at = timezone.now()
            document.save(update_fields=['is_verified', 'verified_by', 'verified_at'])

            # Log the verification
            log_user_action(
                user=request.user,
                action='verify_document',
                resource_type='TransactionDocument',
                resource_id=str(document.id),
                additional_data={
                    'document_title': document.title,
                    'transaction_number': document.transaction.transaction_number
                }
            )

            logger.info(f"Document verified: {document.title} by {request.user}")

            return Response({
                'message': 'Document verified successfully',
                'document': TransactionDocumentSerializer(document).data
            })

        return Response(
            {'message': 'Document is already verified'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download a transaction document."""
        document = self.get_object()

        # Check if file exists
        if not document.file or not document.file.name:
            return Response(
                {'error': 'File not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            from django.http import FileResponse
            import os

            # Check if file exists on disk
            if not os.path.exists(document.file.path):
                return Response(
                    {'error': 'File not found on disk'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Create file response
            response = FileResponse(
                open(document.file.path, 'rb'),
                content_type=document.mime_type or 'application/octet-stream'
            )
            response['Content-Disposition'] = f'attachment; filename="{document.title}.{document.get_file_extension()}"'

            # Log the download
            log_user_action(
                user=request.user,
                action='download_document',
                model_name='TransactionDocument',
                object_id=str(document.id),
                object_repr=str(document),
                ip_address=get_client_ip(request),
                additional_data={
                    'document_title': document.title,
                    'transaction_number': document.transaction.transaction_number
                }
            )

            logger.info(f"Document downloaded: {document.title} by {request.user}")

            return response

        except Exception as e:
            logger.error(f"Error downloading document {document.id}: {e}")
            return Response(
                {'error': 'Error downloading file'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple documents for a transaction."""
        try:
            # Debug request data
            logger.info(f"Bulk document upload request from {request.user}")
            logger.info(f"Request content type: {request.content_type}")
            logger.info(f"Request method: {request.method}")
            logger.info(f"Request data keys: {list(request.data.keys()) if hasattr(request, 'data') else 'No data'}")
            logger.info(f"Request FILES keys: {list(request.FILES.keys()) if hasattr(request, 'FILES') else 'No FILES'}")
            logger.info(f"Transaction ID: {request.data.get('transaction_id') if hasattr(request, 'data') else 'No data'}")
            logger.info(f"Document type: {request.data.get('document_type') if hasattr(request, 'data') else 'No data'}")

            files = request.FILES.getlist('files')
            logger.info(f"Files found: {len(files)}")
            for i, file in enumerate(files):
                logger.info(f"File {i}: {file.name} ({file.size} bytes, {file.content_type})")

            from .serializers import BulkDocumentUploadSerializer

            # Prepare data for serializer
            data = {
                'transaction_id': request.data.get('transaction_id'),
                'document_type': request.data.get('document_type'),
                'files': files
            }

            logger.info(f"Serializer data: transaction_id={data['transaction_id']}, document_type={data['document_type']}, files_count={len(data['files'])}")

            serializer = BulkDocumentUploadSerializer(data=data, context={'request': request})

            if serializer.is_valid():
                transaction_id = serializer.validated_data['transaction_id']
                files = serializer.validated_data['files']
                document_type = serializer.validated_data['document_type']

                # Get transaction (validation already done in serializer)
                transaction_obj = Transaction.objects.get(
                    id=transaction_id,
                    is_deleted=False
                )

                logger.info(f"Uploading {len(files)} documents for transaction {transaction_obj.transaction_number}")

            else:
                logger.warning(f"Bulk upload validation failed: {serializer.errors}")

                # Provide detailed error information
                error_details = {}
                for field, errors in serializer.errors.items():
                    if isinstance(errors, list):
                        error_details[field] = errors
                    else:
                        error_details[field] = [str(errors)]

                return Response({
                    'error': 'Validation failed',
                    'details': error_details,
                    'received_data': {
                        'transaction_id': request.data.get('transaction_id'),
                        'document_type': request.data.get('document_type'),
                        'files_count': len(request.FILES.getlist('files')),
                        'file_names': [f.name for f in request.FILES.getlist('files')]
                    }
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create documents
            created_documents = []
            failed_uploads = []

            with transaction.atomic():
                for i, file in enumerate(files):
                    try:
                        # Generate a meaningful title
                        file_name = file.name.rsplit('.', 1)[0]  # Remove extension
                        title = f"{document_type.replace('_', ' ').title()}: {file_name}"

                        document = TransactionDocument.objects.create(
                            transaction=transaction_obj,
                            document_type=document_type,
                            title=title,
                            file=file,
                            uploaded_by=request.user
                        )
                        created_documents.append(document)
                        logger.info(f"Document created: {document.title} for transaction {transaction_obj.transaction_number}")

                    except Exception as e:
                        logger.error(f"Failed to create document {i+1}: {str(e)}")
                        failed_uploads.append({
                            'file_name': file.name,
                            'error': str(e)
                        })

            # Check if any uploads failed
            if failed_uploads:
                logger.warning(f"Some documents failed to upload: {failed_uploads}")
                return Response({
                    'error': 'Some documents failed to upload',
                    'successful_uploads': len(created_documents),
                    'failed_uploads': failed_uploads,
                    'documents': TransactionDocumentSerializer(created_documents, many=True).data
                }, status=status.HTTP_207_MULTI_STATUS)

            # Log the bulk upload
            log_user_action(
                user=request.user,
                action='bulk_upload_documents',
                model_name='TransactionDocument',
                object_id=str(transaction_obj.id),
                object_repr=f'{transaction_obj.transaction_number} - {len(files)} documents',
                ip_address=get_client_ip(request),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'document_count': len(files),
                    'document_type': document_type
                }
            )

            logger.info(f"Bulk upload completed: {len(files)} documents for {transaction_obj.transaction_number} by {request.user}")

            return Response({
                'message': f'{len(files)} documents uploaded successfully',
                'documents': TransactionDocumentSerializer(created_documents, many=True).data
            })

        except Exception as e:
            logger.error(f"Bulk upload failed with exception: {str(e)}", exc_info=True)
            return Response({
                'error': 'Document upload failed',
                'message': str(e),
                'details': 'An unexpected error occurred during document upload'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def customer_balance_history(self, request):
        """Get balance history for a specific customer."""
        customer_id = request.query_params.get('customer')
        if not customer_id:
            return Response(
                {'error': 'Customer ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        location_id = request.query_params.get('location')
        currency_code = request.query_params.get('currency')

        # Base queryset for the customer
        queryset = self.get_queryset().filter(customer_id=customer_id)

        # Apply additional filters
        if location_id:
            queryset = queryset.filter(location_id=location_id)
        if currency_code:
            queryset = queryset.filter(currency__code=currency_code)

        # Order by creation date to show balance progression
        queryset = queryset.order_by('created_at')

        # Serialize the data
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def company_balances(self, request):
        """Get current company balances by location and currency."""
        user = request.user

        # Get query parameters
        location_id = request.query_params.get('location')
        currency_code = request.query_params.get('currency')

        # Base queryset for company balances (customer is null)
        queryset = self.get_queryset().filter(customer__isnull=True)

        # Apply filters
        if location_id:
            queryset = queryset.filter(location_id=location_id)
        if currency_code:
            queryset = queryset.filter(currency__code=currency_code)

        # Calculate current company balances
        company_balances = {}

        # Group by location and currency
        for entry in queryset.order_by('location', 'currency', '-created_at'):
            key = f"{entry.location_id}_{entry.currency_id}"

            if key not in company_balances:
                company_balances[key] = {
                    'location': entry.location.name,
                    'location_id': entry.location_id,
                    'currency': entry.currency.code,
                    'currency_id': entry.currency_id,
                    'currency_symbol': entry.currency.symbol,
                    'balance': entry.running_balance or 0,
                    'formatted_balance': entry.currency.format_amount_with_symbol(entry.running_balance or 0),
                    'last_updated': entry.created_at
                }

        return Response(list(company_balances.values()))

    @action(detail=False, methods=['get'])
    def balance_summary(self, request):
        """Get a summary of all balances grouped by customer, location, and currency."""
        user = request.user

        # Get all balance entries
        queryset = self.get_queryset()

        # Calculate summary data
        summary = {
            'customer_balances': {},
            'company_balances': {},
            'total_balances_by_currency': {},
            'locations': [],
            'currencies': []
        }

        # Process all balance entries
        for entry in queryset.order_by('customer', 'location', 'currency', '-created_at'):
            # Customer balances
            if entry.customer:
                customer_key = f"{entry.customer_id}_{entry.location_id}_{entry.currency_id}"
                if customer_key not in summary['customer_balances']:
                    summary['customer_balances'][customer_key] = {
                        'customer': entry.customer.get_display_name(),
                        'customer_id': entry.customer_id,
                        'location': entry.location.name,
                        'location_id': entry.location_id,
                        'currency': entry.currency.code,
                        'currency_id': entry.currency_id,
                        'currency_symbol': entry.currency.symbol,
                        'balance': entry.running_balance or 0,
                        'formatted_balance': entry.currency.format_amount_with_symbol(entry.running_balance or 0),
                        'last_updated': entry.created_at
                    }

            # Company balances
            else:
                company_key = f"{entry.location_id}_{entry.currency_id}"
                if company_key not in summary['company_balances']:
                    summary['company_balances'][company_key] = {
                        'location': entry.location.name,
                        'location_id': entry.location_id,
                        'currency': entry.currency.code,
                        'currency_id': entry.currency_id,
                        'currency_symbol': entry.currency.symbol,
                        'balance': entry.running_balance or 0,
                        'formatted_balance': entry.currency.format_amount_with_symbol(entry.running_balance or 0),
                        'last_updated': entry.created_at
                    }

            # Track unique locations and currencies
            location_info = {'id': entry.location_id, 'name': entry.location.name}
            if location_info not in summary['locations']:
                summary['locations'].append(location_info)

            currency_info = {'id': entry.currency_id, 'code': entry.currency.code, 'symbol': entry.currency.symbol}
            if currency_info not in summary['currencies']:
                summary['currencies'].append(currency_info)

        # Calculate total balances by currency
        for currency_info in summary['currencies']:
            currency_code = currency_info['code']
            total_customer_balance = sum(
                balance['balance'] for balance in summary['customer_balances'].values()
                if balance['currency'] == currency_code
            )
            total_company_balance = sum(
                balance['balance'] for balance in summary['company_balances'].values()
                if balance['currency'] == currency_code
            )

            summary['total_balances_by_currency'][currency_code] = {
                'currency': currency_code,
                'currency_symbol': currency_info['symbol'],
                'total_customer_balance': total_customer_balance,
                'total_company_balance': total_company_balance,
                'net_balance': total_customer_balance + total_company_balance,
                'formatted_customer_balance': f"{currency_info['symbol']} {total_customer_balance:,.2f}",
                'formatted_company_balance': f"{currency_info['symbol']} {total_company_balance:,.2f}",
                'formatted_net_balance': f"{currency_info['symbol']} {total_customer_balance + total_company_balance:,.2f}"
            }

        # Convert dictionaries to lists for easier frontend consumption
        summary['customer_balances'] = list(summary['customer_balances'].values())
        summary['company_balances'] = list(summary['company_balances'].values())

        return Response(summary)


class CommissionRuleViewSet(viewsets.ModelViewSet):
    """ViewSet for Commission Rule management."""

    queryset = CommissionRule.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'priority', 'created_at']
    ordering = ['priority', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return CommissionRuleListSerializer
        return CommissionRuleSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = super().get_queryset()

        # Non-admin users can only see rules for their location
        if not self.request.user.can_manage_users():
            if self.request.user.location:
                queryset = queryset.filter(
                    Q(location=self.request.user.location) | Q(location__isnull=True)
                )
            else:
                queryset = queryset.filter(location__isnull=True)

        # Filter by query parameters
        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)

        transaction_type_id = self.request.query_params.get('transaction_type')
        if transaction_type_id:
            queryset = queryset.filter(transaction_type_id=transaction_type_id)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    def perform_create(self, serializer):
        """Create commission rule with audit logging."""
        rule = serializer.save(created_by=self.request.user)

        # Check for conflicts
        conflicts = commission_rule_manager.validate_rule_conflicts(rule)
        if conflicts:
            logger.warning(f"Commission rule {rule.name} has conflicts with {len(conflicts)} existing rules")

        # Log the creation
        log_user_action(
            user=self.request.user,
            action='create_commission_rule',
            model_name='CommissionRule',
            object_id=str(rule.id),
            object_repr=str(rule),
            ip_address=get_client_ip(self.request),
            additional_data={
                'rule_name': rule.name,
                'commission_type': rule.commission_type,
                'location': rule.location.name if rule.location else 'All'
            }
        )

        logger.info(f"Commission rule created: {rule.name} by {self.request.user}")

    def perform_update(self, serializer):
        """Update commission rule with audit logging."""
        rule = serializer.save(updated_by=self.request.user)

        # Log the update
        log_user_action(
            user=self.request.user,
            action='update_commission_rule',
            model_name='CommissionRule',
            object_id=str(rule.id),
            object_repr=str(rule),
            ip_address=get_client_ip(self.request),
            additional_data={
                'rule_name': rule.name,
                'commission_type': rule.commission_type
            }
        )

        logger.info(f"Commission rule updated: {rule.name} by {self.request.user}")

    @action(detail=False, methods=['post'])
    def calculate_commission(self, request):
        """Calculate commission for given transaction parameters."""
        logger.info(f"Commission calculation request from {request.user}: {request.data}")

        serializer = CommissionCalculationSerializer(data=request.data)

        if serializer.is_valid():
            logger.info(f"Commission calculation data validated: {serializer.validated_data}")

            # Check if there are any commission rules in the system
            total_rules = CommissionRule.objects.filter(is_active=True, is_deleted=False).count()
            logger.info(f"Total active commission rules in system: {total_rules}")

            # Convert UUIDs to model objects
            from apps.locations.models import Location
            from apps.currencies.models import Currency
            from apps.transactions.models import TransactionType

            validated_data = serializer.validated_data.copy()

            try:
                validated_data['location'] = Location.objects.get(id=validated_data['location'])
                validated_data['from_currency'] = Currency.objects.get(id=validated_data['from_currency'])
                validated_data['to_currency'] = Currency.objects.get(id=validated_data['to_currency'])

                if validated_data.get('transaction_type'):
                    validated_data['transaction_type'] = TransactionType.objects.get(id=validated_data['transaction_type'])

                commission_result = commission_calculator.get_commission_preview(validated_data)
                logger.info(f"Commission calculation result: {commission_result}")

                result_serializer = CommissionCalculationResultSerializer(commission_result)

                return Response({
                    'commission': result_serializer.data,
                    'message': 'Commission calculated successfully'
                })
            except (Location.DoesNotExist, Currency.DoesNotExist, TransactionType.DoesNotExist) as e:
                logger.error(f"Invalid reference in commission calculation: {str(e)}")
                return Response({
                    'error': f'Invalid reference: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

        logger.warning(f"Commission calculation validation failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_default_rules(self, request):
        """Create default commission rules for a location."""
        if not request.user.can_manage_users():
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        location_id = request.data.get('location_id')
        location = None

        if location_id:
            from apps.locations.models import Location
            try:
                location = Location.objects.get(id=location_id)
            except Location.DoesNotExist:
                return Response(
                    {'error': 'Location not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        rules = commission_rule_manager.create_default_rules(location)

        return Response({
            'message': f'Created {len(rules)} default commission rules',
            'rules': [rule.name for rule in rules]
        })

    @action(detail=True, methods=['get'])
    def conflicts(self, request, pk=None):
        """Check for conflicts with other commission rules."""
        rule = self.get_object()
        conflicts = commission_rule_manager.validate_rule_conflicts(rule)

        conflict_data = []
        for conflict in conflicts:
            conflict_data.append({
                'id': conflict.id,
                'name': conflict.name,
                'priority': conflict.priority,
                'commission_type': conflict.get_commission_type_display(),
                'location': conflict.location.name if conflict.location else 'All'
            })

        return Response({
            'conflicts': conflict_data,
            'count': len(conflicts)
        })


class CommissionTierViewSet(viewsets.ModelViewSet):
    """ViewSet for Commission Tier management."""

    queryset = CommissionTier.objects.filter(is_deleted=False)
    serializer_class = CommissionTierSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter tiers by commission rule."""
        queryset = super().get_queryset()

        rule_id = self.request.query_params.get('rule')
        if rule_id:
            queryset = queryset.filter(commission_rule_id=rule_id)

        return queryset.order_by('commission_rule', 'min_amount')

    def perform_create(self, serializer):
        """Create commission tier with audit logging."""
        tier = serializer.save(created_by=self.request.user)

        # Log the creation
        log_user_action(
            user=self.request.user,
            action='create_commission_tier',
            model_name='CommissionTier',
            object_id=str(tier.id),
            object_repr=str(tier),
            ip_address=get_client_ip(self.request),
            additional_data={
                'rule_name': tier.commission_rule.name,
                'min_amount': str(tier.min_amount),
                'max_amount': str(tier.max_amount) if tier.max_amount else 'unlimited'
            }
        )

        logger.info(f"Commission tier created for rule {tier.commission_rule.name} by {self.request.user}")
