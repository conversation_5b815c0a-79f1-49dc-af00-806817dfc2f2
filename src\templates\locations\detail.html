{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Location Details" %}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/datatables.min.css' %}" rel="stylesheet">
<style>
    .info-card {
        background: white;
        border-radius: 0;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .info-card .card-header {
        background-color: #000d28;
        color: white;
        border-bottom: 1px solid #dee2e6;
    }
    .metric-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0;
        transition: transform 0.2s;
    }
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #000d28;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">{% trans "Location Details" %}</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'locations_web:list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        {% trans "Back to List" %}
                    </a>
                    <a href="#" class="btn btn-primary" id="edit-location">
                        <i class="bi bi-pencil"></i>
                        {% trans "Edit" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt me-2"></i>
                        {% trans "Location Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Name" %}:</label>
                                <p class="mb-0" id="location-name">-</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Code" %}:</label>
                                <p class="mb-0" id="location-code">-</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Type" %}:</label>
                                <p class="mb-0" id="location-type">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Status" %}:</label>
                                <p class="mb-0" id="location-status">-</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Address" %}:</label>
                                <p class="mb-0" id="location-address">-</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Phone" %}:</label>
                                <p class="mb-0" id="location-phone">-</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock me-2"></i>
                        {% trans "Timestamps" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">{% trans "Created" %}:</label>
                        <p class="mb-0" id="location-created">-</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">{% trans "Updated" %}:</label>
                        <p class="mb-0" id="location-updated">-</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card text-center p-3">
                <div class="metric-value" id="total-transactions">0</div>
                <div class="metric-label">{% trans "Total Transactions" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card text-center p-3">
                <div class="metric-value" id="active-customers">0</div>
                <div class="metric-label">{% trans "Active Customers" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card text-center p-3">
                <div class="metric-value" id="monthly-volume">$0</div>
                <div class="metric-label">{% trans "Monthly Volume" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card text-center p-3">
                <div class="metric-value" id="total-balance">$0</div>
                <div class="metric-label">{% trans "Total Balance" %}</div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="card info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        {% trans "Recent Transactions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="recent-transactions-table" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{% trans "Transaction #" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Customer" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- DataTables will populate this -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/datatables.min.js' %}"></script>
<script>
let locationId = '{{ location.id }}';
let recentTransactionsTable;

document.addEventListener('DOMContentLoaded', function() {
    loadLocationDetails();
    initializeRecentTransactionsTable();
});

function loadLocationDetails() {
    // Get location ID from URL
    const pathParts = window.location.pathname.split('/');
    locationId = pathParts[pathParts.length - 2]; // Get ID from URL

    console.log('Loading location details for ID:', locationId);

    // Load location details
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            populateLocationDetails(data);
            loadLocationStats();
        },
        error: function(xhr) {
            console.error('Error loading location details:', xhr);
            showAlert('danger', '{% trans "Failed to load location details" %}');
        }
    });
}

function populateLocationDetails(location) {
    $('#location-name').text(location.name || '-');
    $('#location-code').text(location.code || '-');
    $('#location-type').text(location.get_location_type_display || location.location_type || '-');
    $('#location-status').html(location.is_active ? 
        '<span class="badge bg-success">{% trans "Active" %}</span>' : 
        '<span class="badge bg-danger">{% trans "Inactive" %}</span>');
    $('#location-address').text(location.address || '-');
    $('#location-phone').text(location.phone || '-');
    $('#location-created').text(new Date(location.created_at).toLocaleString());
    $('#location-updated').text(new Date(location.updated_at).toLocaleString());

    // Update edit button
    $('#edit-location').attr('href', `/locations/${location.id}/edit/`);
}

function loadLocationStats() {
    // Load location statistics
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/stats/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#total-transactions').text(data.total_transactions || 0);
            $('#active-customers').text(data.active_customers || 0);
            $('#monthly-volume').text(data.monthly_volume || '$0');
            $('#total-balance').text(data.total_balance || '$0');
        },
        error: function(xhr) {
            console.error('Error loading location stats:', xhr);
        }
    });
}

function initializeRecentTransactionsTable() {
    recentTransactionsTable = $('#recent-transactions-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/api/v1/transactions/transactions/',
            type: 'GET',
            headers: getAuthHeaders(),
            data: function(d) {
                return {
                    page: Math.floor(d.start / d.length) + 1,
                    page_size: d.length,
                    search: d.search.value,
                    location: locationId,
                    ordering: d.order.length > 0 ?
                        (d.order[0].dir === 'desc' ? '-' : '') + 
                        ['transaction_number', 'created_at', 'customer__first_name', 'transaction_type__name', 'from_amount', 'status'][d.order[0].column] :
                        '-created_at'
                };
            },
            error: function(xhr, error, thrown) {
                console.error('Recent transactions DataTables AJAX error:', error, thrown);
                showAlert('danger', '{% trans "Failed to load recent transactions" %}');
            }
        },
        columns: [
            { data: 'transaction_number', name: 'transaction_number' },
            { data: 'created_at_display', name: 'created_at' },
            { data: 'customer_name', name: 'customer__first_name' },
            { data: 'transaction_type_name', name: 'transaction_type__name' },
            { data: 'from_amount_display', name: 'from_amount' },
            { data: 'status_display', name: 'status' },
            { data: null, orderable: false }
        ],
        order: [[1, 'desc']], // Order by date
        pageLength: 10,
        lengthMenu: [[5, 10, 25], [5, 10, 25]],
        responsive: true,
        columnDefs: [
            {
                targets: -1, // Actions column
                render: function(data, type, row) {
                    return `
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="/transactions/detail/${row.id}/" class="btn btn-outline-primary btn-sm" title="{% trans 'View Details' %}">
                                <i class="bi bi-eye"></i>
                            </a>
                        </div>
                    `;
                }
            }
        ],
        language: {
            search: '{% trans "Search:" %}',
            lengthMenu: '{% trans "Show _MENU_ entries" %}',
            info: '{% trans "Showing _START_ to _END_ of _TOTAL_ entries" %}',
            infoEmpty: '{% trans "Showing 0 to 0 of 0 entries" %}',
            infoFiltered: '{% trans "(filtered from _MAX_ total entries)" %}',
            loadingRecords: '{% trans "Loading..." %}',
            processing: '{% trans "Processing..." %}',
            zeroRecords: '{% trans "No recent transactions found" %}',
            paginate: {
                first: '{% trans "First" %}',
                last: '{% trans "Last" %}',
                next: '{% trans "Next" %}',
                previous: '{% trans "Previous" %}'
            }
        }
    });
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}
</script>
{% endblock %}
