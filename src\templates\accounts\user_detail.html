{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ user_obj.get_display_name }} - {% trans "User Details" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-person"></i>
                {{ user_obj.get_display_name }}
                {% if not user_obj.is_active %}
                    <span class="badge bg-secondary ms-2">{% trans "Inactive" %}</span>
                {% endif %}
                {% if user_obj.is_account_locked %}
                    <span class="badge bg-danger ms-2">{% trans "Locked" %}</span>
                {% endif %}
            </h1>
            <div class="btn-group" role="group">
                <a href="/users/" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to Users" %}
                </a>
                {% if request.user.can_manage_users %}
                    <button class="btn btn-primary" onclick="editUser('{{ user_obj.id }}')">
                        <i class="bi bi-pencil"></i>
                        {% trans "Edit User" %}
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Information Cards -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i>
                    {% trans "Basic Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Username" %}:</strong></div>
                    <div class="col-sm-8"><code>{{ user_obj.username }}</code></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Full Name" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.get_full_name|default:"-" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Email" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.email %}
                            <a href="mailto:{{ user_obj.email }}">{{ user_obj.email }}</a>
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Phone" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.phone_number %}
                            <a href="tel:{{ user_obj.phone_number }}">{{ user_obj.phone_number }}</a>
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Employee ID" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.employee_id|default:"-" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Department" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.department|default:"-" }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Role & Permissions -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i>
                    {% trans "Role & Permissions" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Role" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.role == 'admin' %}
                            <span class="badge bg-danger">{{ user_obj.get_role_display }}</span>
                        {% elif user_obj.role == 'accountant' %}
                            <span class="badge bg-primary">{{ user_obj.get_role_display }}</span>
                        {% elif user_obj.role == 'branch_employee' %}
                            <span class="badge bg-success">{{ user_obj.get_role_display }}</span>
                        {% elif user_obj.role == 'viewer' %}
                            <span class="badge bg-info">{{ user_obj.get_role_display }}</span>
                        {% elif user_obj.role == 'courier' %}
                            <span class="badge bg-warning">{{ user_obj.get_role_display }}</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ user_obj.get_role_display }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Location" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.location %}
                            <i class="bi bi-geo-alt"></i>
                            {{ user_obj.location.name }}
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Status" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.is_account_locked %}
                            <span class="badge bg-danger">{% trans "Locked" %}</span>
                        {% elif user_obj.is_active %}
                            <span class="badge bg-success">{% trans "Active" %}</span>
                        {% else %}
                            <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Superuser" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.is_superuser %}
                            <span class="badge bg-warning">{% trans "Yes" %}</span>
                        {% else %}
                            <span class="badge bg-secondary">{% trans "No" %}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Staff" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.is_staff %}
                            <span class="badge bg-info">{% trans "Yes" %}</span>
                        {% else %}
                            <span class="badge bg-secondary">{% trans "No" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Account Activity -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Account Activity" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Date Joined" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.date_joined|date:"Y-m-d H:i" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Last Login" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.last_login %}
                            {{ user_obj.last_login|date:"Y-m-d H:i" }}
                        {% else %}
                            <span class="text-muted">{% trans "Never" %}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Failed Attempts" %}:</strong></div>
                    <div class="col-sm-8">
                        {% if user_obj.failed_login_attempts > 0 %}
                            <span class="badge bg-warning">{{ user_obj.failed_login_attempts }}</span>
                        {% else %}
                            <span class="badge bg-success">0</span>
                        {% endif %}
                    </div>
                </div>
                {% if user_obj.account_locked_until %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Locked Until" %}:</strong></div>
                    <div class="col-sm-8">
                        <span class="text-danger">{{ user_obj.account_locked_until|date:"Y-m-d H:i" }}</span>
                    </div>
                </div>
                {% endif %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Last Updated" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.updated_at|date:"Y-m-d H:i" }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {% trans "Additional Information" %}
                </h5>
            </div>
            <div class="card-body">
                {% if user_obj.notes %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Notes" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.notes|linebreaks }}</div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "User ID" %}:</strong></div>
                    <div class="col-sm-8"><code>{{ user_obj.id }}</code></div>
                </div>
                
                {% if user_obj.created_by %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Created By" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.created_by.get_display_name }}</div>
                </div>
                {% endif %}
                
                {% if user_obj.updated_by %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>{% trans "Updated By" %}:</strong></div>
                    <div class="col-sm-8">{{ user_obj.updated_by.get_display_name }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
{% if request.user.can_manage_users %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i>
                    {% trans "Quick Actions" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary" onclick="editUser('{{ user_obj.id }}')">
                        <i class="bi bi-pencil"></i>
                        {% trans "Edit User" %}
                    </button>
                    
                    {% if user_obj.is_account_locked %}
                        <button class="btn btn-outline-success" onclick="unlockUser('{{ user_obj.id }}')">
                            <i class="bi bi-unlock"></i>
                            {% trans "Unlock Account" %}
                        </button>
                    {% endif %}
                    
                    {% if user_obj.is_active %}
                        <button class="btn btn-outline-warning" onclick="deactivateUser('{{ user_obj.id }}')">
                            <i class="bi bi-pause"></i>
                            {% trans "Deactivate" %}
                        </button>
                    {% else %}
                        <button class="btn btn-outline-success" onclick="activateUser('{{ user_obj.id }}')">
                            <i class="bi bi-play"></i>
                            {% trans "Activate" %}
                        </button>
                    {% endif %}
                    
                    {% if user_obj.failed_login_attempts > 0 %}
                        <button class="btn btn-outline-info" onclick="resetFailedAttempts('{{ user_obj.id }}')">
                            <i class="bi bi-arrow-clockwise"></i>
                            {% trans "Reset Failed Attempts" %}
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function editUser(userId) {
    window.location.href = `/users/?edit=${userId}`;
}

function unlockUser(userId) {
    if (!confirm('{% trans "Are you sure you want to unlock this user account?" %}')) {
        return;
    }
    
    $.ajax({
        url: `/accounts/api/users/${userId}/unlock/`,
        method: 'POST',
        headers: getAuthHeaders(),
        success: function(response) {
            location.reload();
        },
        error: function(xhr) {
            console.error('Error unlocking user:', xhr);
            alert('{% trans "Error unlocking user account" %}');
        }
    });
}

function activateUser(userId) {
    updateUserStatus(userId, true, '{% trans "activate" %}');
}

function deactivateUser(userId) {
    updateUserStatus(userId, false, '{% trans "deactivate" %}');
}

function updateUserStatus(userId, isActive, action) {
    if (!confirm(`{% trans "Are you sure you want to" %} ${action} {% trans "this user?" %}`)) {
        return;
    }
    
    $.ajax({
        url: `/api/v1/accounts/users/${userId}/`,
        method: 'PATCH',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify({ is_active: isActive }),
        success: function(response) {
            location.reload();
        },
        error: function(xhr) {
            console.error('Error updating user status:', xhr);
            alert('{% trans "Error updating user status" %}');
        }
    });
}

function resetFailedAttempts(userId) {
    if (!confirm('{% trans "Are you sure you want to reset failed login attempts?" %}')) {
        return;
    }
    
    $.ajax({
        url: `/api/v1/accounts/users/${userId}/reset_failed_attempts/`,
        method: 'POST',
        headers: getAuthHeaders(),
        success: function(response) {
            location.reload();
        },
        error: function(xhr) {
            console.error('Error resetting failed attempts:', xhr);
            alert('{% trans "Error resetting failed attempts" %}');
        }
    });
}

function getAuthHeaders() {
    const token = localStorage.getItem('arena_doviz_access_token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}
</script>
{% endblock %}
