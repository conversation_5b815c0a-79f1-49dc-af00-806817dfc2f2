"""
Serializers for Arena Doviz Customers app.
"""

from rest_framework import serializers
from .models import Customer, CustomerContact, CustomerDocument
import logging

logger = logging.getLogger(__name__)


class CustomerContactSerializer(serializers.ModelSerializer):
    """Serializer for CustomerContact model."""
    
    class Meta:
        model = CustomerContact
        fields = [
            'id', 'contact_type', 'name', 'title', 'phone_number',
            'email', 'is_primary', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CustomerDocumentSerializer(serializers.ModelSerializer):
    """Serializer for CustomerDocument model."""

    file_size_formatted = serializers.CharField(source='get_file_size_formatted', read_only=True)
    file_extension = serializers.CharField(source='get_file_extension', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    verified_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerDocument
        fields = [
            'id', 'customer', 'document_type', 'title', 'file', 'file_size',
            'file_size_formatted', 'file_extension', 'mime_type',
            'expiry_date', 'is_verified', 'verified_by', 'verified_by_name',
            'verified_at', 'notes', 'is_expired', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'file_size', 'file_size_formatted', 'file_extension',
            'mime_type', 'verified_by', 'verified_by_name', 'verified_at',
            'is_expired', 'created_at', 'updated_at'
        ]

    def get_verified_by_name(self, obj):
        """Get the display name of the user who verified the document."""
        if obj.verified_by:
            return obj.verified_by.get_display_name()
        return None

    def validate(self, attrs):
        """Validate and auto-generate title if empty."""
        # Auto-generate title if not provided
        if not attrs.get('title') or attrs.get('title').strip() == '':
            document_type = attrs.get('document_type', '')
            file_obj = attrs.get('file')

            if file_obj and hasattr(file_obj, 'name'):
                # Use file name without extension
                file_name = file_obj.name.rsplit('.', 1)[0]
                # Format document type for display
                type_display = document_type.replace('_', ' ').title()
                attrs['title'] = f"{type_display}: {file_name}"
            else:
                # Fallback title
                type_display = document_type.replace('_', ' ').title()
                attrs['title'] = f"{type_display} Document"

        return attrs


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer for Customer model."""
    
    # Related fields
    contacts = CustomerContactSerializer(many=True, read_only=True)
    documents = CustomerDocumentSerializer(many=True, read_only=True)
    
    # Display fields
    customer_type_display = serializers.CharField(source='get_customer_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    contact_info = serializers.CharField(source='get_contact_info', read_only=True)
    full_address = serializers.CharField(source='get_full_address', read_only=True)
    
    # Currency and location names
    preferred_currency_code = serializers.CharField(source='preferred_currency.code', read_only=True)
    preferred_location_name = serializers.CharField(source='preferred_location.name', read_only=True)
    
    # Statistics
    balance_summary = serializers.SerializerMethodField()
    transaction_count = serializers.SerializerMethodField()
    last_transaction_date = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = Customer
        fields = [
            'id', 'customer_type', 'customer_type_display', 'first_name', 'last_name',
            'company_name', 'phone_number', 'email', 'address', 'city', 'country',
            'description', 'whatsapp_group_id', 'status', 'status_display',
            'credit_limit', 'preferred_currency', 'preferred_currency_code',
            'preferred_location', 'preferred_location_name', 'notes',
            'customer_code', 'risk_level', 'registration_date',
            'last_transaction_date', 'display_name', 'full_name',
            'contact_info', 'full_address', 'balance_summary',
            'transaction_count', 'contacts', 'documents',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'customer_code', 'registration_date', 'last_transaction_date',
            'created_at', 'updated_at'
        ]
    
    def get_balance_summary(self, obj):
        """Get customer balance summary."""
        try:
            return obj.get_balance_summary()
        except Exception as e:
            logger.error(f"Error getting balance summary for customer {obj.customer_code}: {e}")
            return {}
    
    def get_transaction_count(self, obj):
        """Get customer transaction count."""
        try:
            return obj.get_transaction_count()
        except Exception as e:
            logger.error(f"Error getting transaction count for customer {obj.customer_code}: {e}")
            return 0
    
    def validate_phone_number(self, value):
        """Validate phone number format."""
        # Allow empty phone numbers
        if not value or value.strip() == '':
            return ''

        # Clean the phone number for validation
        cleaned_value = value.replace('+', '').replace('-', '').replace(' ', '').replace('(', '').replace(')', '')

        if not cleaned_value.isdigit():
            raise serializers.ValidationError("Phone number must contain only digits, spaces, hyphens, parentheses, and plus sign")

        return value
    
    def validate(self, attrs):
        """Validate customer data based on customer type."""
        customer_type = attrs.get('customer_type', getattr(self.instance, 'customer_type', None))
        
        if customer_type == Customer.CustomerType.INDIVIDUAL:
            if not attrs.get('first_name') and not attrs.get('last_name'):
                if not (self.instance and (self.instance.first_name or self.instance.last_name)):
                    raise serializers.ValidationError({
                        'first_name': 'First name or last name is required for individual customers'
                    })
        
        elif customer_type == Customer.CustomerType.CORPORATE:
            if not attrs.get('company_name'):
                if not (self.instance and self.instance.company_name):
                    raise serializers.ValidationError({
                        'company_name': 'Company name is required for corporate customers'
                    })
        
        return attrs


class CustomerListSerializer(serializers.ModelSerializer):
    """Simplified serializer for customer list views."""
    
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    customer_type_display = serializers.CharField(source='get_customer_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    preferred_currency_code = serializers.CharField(source='preferred_currency.code', read_only=True)
    preferred_location_name = serializers.CharField(source='preferred_location.name', read_only=True)
    
    class Meta:
        model = Customer
        fields = [
            'id', 'customer_code', 'display_name', 'customer_type',
            'customer_type_display', 'phone_number', 'email', 'status',
            'status_display', 'preferred_currency_code', 'preferred_location_name',
            'whatsapp_group_id', 'registration_date', 'last_transaction_date'
        ]


class CustomerCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating customers."""
    
    class Meta:
        model = Customer
        fields = [
            'customer_type', 'first_name', 'last_name', 'company_name',
            'phone_number', 'email', 'address', 'city', 'country',
            'description', 'whatsapp_group_id', 'credit_limit',
            'preferred_currency', 'preferred_location', 'notes', 'risk_level'
        ]
    
    def validate(self, attrs):
        """Validate customer data."""
        customer_type = attrs.get('customer_type')
        
        if customer_type == Customer.CustomerType.INDIVIDUAL:
            if not attrs.get('first_name') and not attrs.get('last_name'):
                raise serializers.ValidationError({
                    'first_name': 'First name or last name is required for individual customers'
                })
        
        elif customer_type == Customer.CustomerType.CORPORATE:
            if not attrs.get('company_name'):
                raise serializers.ValidationError({
                    'company_name': 'Company name is required for corporate customers'
                })
        
        return attrs


class CustomerStatsSerializer(serializers.Serializer):
    """Serializer for customer statistics."""
    
    total_customers = serializers.IntegerField()
    active_customers = serializers.IntegerField()
    individual_customers = serializers.IntegerField()
    corporate_customers = serializers.IntegerField()
    customers_by_status = serializers.DictField()
    customers_by_location = serializers.DictField()
    customers_by_risk_level = serializers.DictField()
    new_customers_this_month = serializers.IntegerField()
    customers_with_negative_balance = serializers.IntegerField()


class CustomerBalanceSerializer(serializers.Serializer):
    """Serializer for customer balance information."""
    
    customer_id = serializers.UUIDField()
    customer_name = serializers.CharField()
    currency_code = serializers.CharField()
    balance = serializers.DecimalField(max_digits=15, decimal_places=6)
    formatted_balance = serializers.CharField()
    last_updated = serializers.DateTimeField()
