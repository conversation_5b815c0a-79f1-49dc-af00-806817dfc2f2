/**
 * External Transfer Transaction Form Handler
 */

class ExternalTransferForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'EXTERNAL_TRANSFER';
        this.customerBalances = [];
        this.initialized = false; // Prevent multiple initializations

        // Only initialize if form exists and not already initialized
        if (this.form && !this.initialized) {
            this.init();
        } else if (this.initialized) {
            console.warn('ExternalTransferForm already initialized, skipping');
        } else {
            console.warn('Transaction form not found, skipping ExternalTransferForm initialization');
        }
    }

    init() {
        if (this.initialized) {
            console.log('ExternalTransferForm already initialized, skipping');
            return;
        }

        console.log('ExternalTransferForm.init() called');
        this.initialized = true; // Mark as initialized

        this.loadFormData();
        this.setupEventListeners();
        this.setupValidation();
        this.setCurrentUser();
    }

    loadFormData() {
        this.loadTransactionTypes();

        // Wait for global TransactionUtils instance to be available
        const waitForTransactionUtils = () => {
            if (window.transactionUtils) {
                window.transactionUtils.loadCustomers();
                window.transactionUtils.loadLocations();
                window.transactionUtils.loadCurrencies();

                // Pre-populate customer from URL parameter if present
                window.transactionUtils.prePopulateCustomerFromUrl();
            } else {
                setTimeout(waitForTransactionUtils, 100);
            }
        };

        waitForTransactionUtils();
    }

    setupEventListeners() {
        // Customer changes
        $('#customer').on('change', () => {
            if (window.transactionUtils) {
                window.transactionUtils.loadCustomerBalance().then(() => {
                    // Update local reference to customer balances
                    this.customerBalances = TransactionUtils.customerBalances || [];
                    this.updateAvailableBalance();
                    this.updateTransactionPreview();
                }).catch(err => {
                    console.error('Error loading customer balance:', err);
                });
            }
        });

        // Amount changes
        $('#from_amount').on('input', () => {
            this.checkSufficientBalance();
            this.updateTransactionPreview();
        });

        // Currency changes
        $('#from_currency').on('change', () => {
            this.checkSufficientBalance();
            this.updateTransactionPreview();
        });

        // Bank details changes
        $('#bank_name, #account_number, #account_name').on('input', () => {
            this.updateTransactionPreview();
        });

        // Commission changes
        $('#commission_amount').on('input', () => {
            this.updateTransactionPreview();
        });
    }

    checkSufficientBalance() {
        const transferAmount = parseFloat($('#from_amount').val()) || 0;
        const selectedCurrencyId = $('#from_currency').val();

        if (!selectedCurrencyId || transferAmount <= 0) {
            $('#balance-warning').empty();
            return;
        }

        // Get currency code from the selected option for balance lookup
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        console.log('Checking balance for currency:', selectedCurrencyCode);

        // Use global TransactionUtils.customerBalances instead of local copy
        const customerBalances = TransactionUtils.customerBalances || this.customerBalances || [];
        console.log('Available customer balances:', customerBalances);

        const balance = customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        const availableBalance = balance ? parseFloat(balance.balance) : 0;

        console.log('Found balance record:', balance);
        console.log('Available balance:', availableBalance);

        // Show warning if insufficient balance
        let warningHtml = '';
        if (transferAmount > availableBalance) {
            if (!customerBalances || customerBalances.length === 0) {
                warningHtml = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Info:</strong> Customer has no balance records. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else if (!balance) {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Customer has no ${selectedCurrencyCode} balance. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Transfer amount (${transferAmount}) exceeds available balance (${availableBalance}) for ${selectedCurrencyCode}.
                    </div>
                `;
            }
        }

        $('#balance-warning').html(warningHtml);
    }

    updateAvailableBalance() {
        const selectedCurrencyId = $('#from_currency').val();
        if (!selectedCurrencyId || !TransactionUtils.customerBalances) {
            return;
        }

        // Get currency code from the selected option
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];

        // Find balance by currency code
        const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        if (balance) {
            this.currentBalance = balance.balance;
        } else {
            this.currentBalance = 0;
        }
    }

    validateForm() {
        let isValid = true;
        const errors = [];

        // Validate amount
        const transferAmount = parseFloat($('#from_amount').val());
        if (transferAmount <= 0) {
            errors.push('Transfer amount must be greater than zero');
            isValid = false;
        }

        // Validate required bank details
        const bankName = $('#bank_name').val().trim();
        const accountNumber = $('#account_number').val().trim();
        const accountName = $('#account_name').val().trim();

        if (!bankName) {
            errors.push('Bank name is required');
            isValid = false;
        }

        if (!accountNumber) {
            errors.push('Account number is required');
            isValid = false;
        }

        if (!accountName) {
            errors.push('Account holder name is required');
            isValid = false;
        }

        // Check sufficient balance
        const selectedCurrencyId = $('#from_currency').val();
        if (TransactionUtils.customerBalances && selectedCurrencyId) {
            // Get currency code from the selected option
            const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
            const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
            const availableBalance = balance ? parseFloat(balance.balance) : 0;

            if (transferAmount > availableBalance) {
                errors.push('Insufficient balance for this transfer');
                isValid = false;
            }
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    prepareFormData(action) {
        const formData = new FormData(this.form);
        const data = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // Set transfer type
        data.transfer_type = 'external';

        // Set to_currency same as from_currency for external transfers
        data.to_currency = data.from_currency;
        data.to_amount = data.from_amount;
        data.exchange_rate = 1.0;

        // Add bank details to description or notes
        const bankDetails = {
            bank_name: data.bank_name,
            account_number: data.account_number,
            account_name: data.account_name,
            bank_code: data.bank_code,
            routing_number: data.routing_number,
            bank_address: data.bank_address
        };

        // Store bank details in additional notes
        const existingNotes = data.additional_notes || '';
        const bankDetailsJson = JSON.stringify(bankDetails);
        data.additional_notes = existingNotes + (existingNotes ? '\n\n' : '') + 'Bank Details: ' + bankDetailsJson;

        return data;
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            amount: formData.get('from_amount'),
            commission: formData.get('commission_amount'),
            bankName: formData.get('bank_name'),
            accountNumber: formData.get('account_number'),
            accountName: formData.get('account_name'),
            purpose: $('#transfer_purpose option:selected').text()
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>From:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.amount && preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Amount:</strong> ${preview.amount} ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.bankName) {
            previewHtml += `<div class="list-group-item"><strong>To Bank:</strong> ${preview.bankName}</div>`;
        }
        
        if (preview.accountName) {
            previewHtml += `<div class="list-group-item"><strong>Beneficiary:</strong> ${preview.accountName}</div>`;
        }
        
        if (preview.accountNumber) {
            previewHtml += `<div class="list-group-item"><strong>Account:</strong> ${preview.accountNumber}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.purpose && preview.purpose !== 'Select purpose...') {
            previewHtml += `<div class="list-group-item"><strong>Purpose:</strong> ${preview.purpose}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    // Common methods
    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const externalTransferType = data.results.find(type => type.code === 'EXTERNAL_TRANSFER');
                if (externalTransferType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: externalTransferType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                TransactionUtils.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter.dataset.action || 'save';
            if (this.validateForm()) {
                this.submitTransaction(action);
            }
        });
    }

    setCurrentUser() {
        const currentUser = ArenaDoviz.auth.getCurrentUser();
        if (currentUser) {
            $('#authorized_by').val(currentUser.display_name || `${currentUser.first_name} ${currentUser.last_name}`);
        }
    }

    getAuthHeaders() {
        // Use the global TransactionUtils method for consistent CSRF handling
        return window.TransactionUtils ? window.TransactionUtils.getAuthHeaders() : {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json',
            'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
        };
    }

    validateForm() {
        // Add validation logic here
        return true;
    }

    submitTransaction(action) {
        if (!this.validateForm()) {
            return;
        }

        TransactionUtils.submitTransaction(this.form, action)
            .then((response) => {
                TransactionUtils.showAlert('success', 'External transfer created successfully');
                setTimeout(() => {
                    window.location.href = `/transactions/${response.id}/`;
                }, 2000);
            })
            .catch((error) => {
                TransactionUtils.showAlert('danger', `Error creating external transfer: ${error.message}`);
            });
    }
}

// Initialize when DOM is ready
$(document).ready(() => {
    new ExternalTransferForm();
});
