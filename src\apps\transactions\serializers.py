"""
Serializers for Arena Doviz Transactions app.
Handles transaction processing, balance entries, and related data serialization.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from decimal import Decimal
from .models import TransactionType, Transaction, BalanceEntry, TransactionDocument
from .commission_models import CommissionRule, CommissionTier
from .commission_utils import commission_calculator
from apps.customers.serializers import CustomerListSerializer
from apps.locations.serializers import LocationListSerializer
from apps.currencies.serializers import CurrencyListSerializer
from apps.core.utils import format_currency, log_user_action, get_client_ip
import logging

logger = logging.getLogger(__name__)


class TransactionTypeSerializer(serializers.ModelSerializer):
    """Serializer for TransactionType model."""
    
    class Meta:
        model = TransactionType
        fields = [
            'id', 'code', 'name', 'description', 'is_exchange',
            'requires_approval', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BalanceEntrySerializer(serializers.ModelSerializer):
    """Serializer for BalanceEntry model."""

    customer_name = serializers.CharField(source='customer.get_display_name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)
    formatted_amount = serializers.SerializerMethodField()
    formatted_running_balance = serializers.SerializerMethodField()
    entry_type_display = serializers.CharField(source='get_entry_type_display', read_only=True)

    # Add fields expected by the frontend
    account_name = serializers.SerializerMethodField()
    balance_after = serializers.CharField(source='running_balance', read_only=True)

    class Meta:
        model = BalanceEntry
        fields = [
            'id', 'transaction', 'customer', 'customer_name', 'location', 'location_name',
            'currency', 'currency_code', 'currency_symbol', 'amount', 'formatted_amount',
            'entry_type', 'entry_type_display', 'description', 'running_balance',
            'formatted_running_balance', 'account_name', 'balance_after', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'running_balance', 'created_at', 'updated_at'
        ]
    
    def get_formatted_amount(self, obj):
        """Return formatted amount with currency symbol."""
        return obj.currency.format_amount_with_symbol(obj.amount)

    def get_formatted_running_balance(self, obj):
        """Return formatted running balance with currency symbol."""
        if obj.running_balance is not None:
            return obj.currency.format_amount_with_symbol(obj.running_balance)
        return None

    def get_account_name(self, obj):
        """Return account name for the balance entry."""
        if obj.customer:
            return f"{obj.customer.get_display_name()} ({obj.location.name})"
        else:
            return f"Company Account ({obj.location.name})"


class TransactionListSerializer(serializers.ModelSerializer):
    """Serializer for Transaction list view."""
    
    customer_name = serializers.CharField(source='customer.get_display_name', read_only=True)
    customer_whatsapp_group_id = serializers.CharField(source='customer.whatsapp_group_id', read_only=True)
    has_whatsapp = serializers.SerializerMethodField()
    location_name = serializers.CharField(source='location.name', read_only=True)
    transaction_type_name = serializers.CharField(source='transaction_type.name', read_only=True)
    from_currency_code = serializers.CharField(source='from_currency.code', read_only=True)
    to_currency_code = serializers.CharField(source='to_currency.code', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    delivery_method_display = serializers.CharField(source='get_delivery_method_display', read_only=True)
    display_amount = serializers.CharField(source='get_display_amount', read_only=True)
    formatted_commission = serializers.SerializerMethodField()
    is_multi_step = serializers.BooleanField(read_only=True)
    is_completed = serializers.BooleanField(read_only=True)
    can_be_approved = serializers.BooleanField(read_only=True)
    can_be_cancelled = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Transaction
        fields = [
            'id', 'transaction_number', 'transaction_type', 'transaction_type_name',
            'customer', 'customer_name', 'customer_whatsapp_group_id', 'has_whatsapp',
            'location', 'location_name', 'from_currency_code', 'to_currency_code',
            'display_amount', 'status', 'status_display', 'delivery_method',
            'delivery_method_display', 'formatted_commission', 'is_multi_step',
            'is_completed', 'can_be_approved', 'can_be_cancelled', 'created_at', 'updated_at'
        ]
    
    def get_formatted_commission(self, obj):
        """Return formatted commission amount."""
        if obj.commission_amount and obj.commission_currency:
            return obj.commission_currency.format_amount_with_symbol(obj.commission_amount)
        return None

    def get_has_whatsapp(self, obj):
        """Return whether customer has WhatsApp group ID configured."""
        return bool(obj.customer.whatsapp_group_id)


class TransactionCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new transactions."""

    # Override fields to handle frontend behavior
    transaction_type = serializers.PrimaryKeyRelatedField(
        queryset=TransactionType.objects.all(),
        required=False,
        allow_null=True
    )
    courier = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)

    # Additional fields for handling courier names
    courier_name = serializers.CharField(max_length=255, required=False, allow_blank=True)
    courier_type = serializers.CharField(max_length=20, required=False, allow_blank=True)

    # Document and file fields (not in model yet)
    document_files = serializers.DictField(required=False)
    document_type = serializers.CharField(max_length=50, required=False, allow_blank=True)

    # Transaction type code field (frontend sends this instead of transaction_type ID sometimes)
    transaction_type_code = serializers.CharField(max_length=50, required=False, allow_blank=True)

    class Meta:
        model = Transaction
        fields = [
            'id', 'transaction_type', 'customer', 'location', 'description',
            'reference_number', 'from_currency', 'to_currency',
            'from_amount', 'to_amount', 'exchange_rate',
            'commission_amount', 'commission_currency',
            'delivery_method', 'courier', 'delivery_address',
            'tracking_code', 'parent_transaction', 'step_number',
            'total_steps', 'notes', 'status', 'courier_name', 'courier_type',
            # Model fields for frontend data
            'deposit_source', 'bank_reference', 'check_number', 'received_by', 'receipt_number',
            'cash_verified', 'customer_id_verified', 'withdrawal_method', 'authorized_by',
            'signature_verified', 'cash_dispensed', 'bank_account',
            'internal_reference', 'external_reference', 'transfer_purpose', 'transfer_instructions',
            'recipient_customer', 'bank_name', 'bank_code', 'account_number', 'account_name',
            'routing_number', 'bank_address', 'document_files', 'document_type', 'transaction_type_code'
        ]
        read_only_fields = ['id']

    def _is_valid_uuid(self, value):
        """Check if a value is a valid UUID string."""
        import re
        uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$', re.IGNORECASE)
        return bool(uuid_pattern.match(str(value)))

    def validate(self, data):
        """Validate transaction data."""
        errors = {}

        # Handle transaction_type_code if provided instead of transaction_type
        if not data.get('transaction_type') and data.get('transaction_type_code'):
            try:
                transaction_type = TransactionType.objects.get(code=data['transaction_type_code'])
                data['transaction_type'] = transaction_type
            except TransactionType.DoesNotExist:
                errors['transaction_type'] = f"Invalid transaction type code: {data['transaction_type_code']}"

        # Ensure transaction_type is provided
        if not data.get('transaction_type'):
            errors['transaction_type'] = _('Transaction type is required')

        # Validate amounts
        from_amount = data.get('from_amount')
        to_amount = data.get('to_amount')

        if from_amount is not None and from_amount <= 0:
            errors['from_amount'] = _('From amount must be positive')

        if to_amount is not None and to_amount <= 0:
            errors['to_amount'] = _('To amount must be positive')

        # Validate currencies are different for exchange transactions
        from_currency = data.get('from_currency')
        to_currency = data.get('to_currency')
        transaction_type = data.get('transaction_type')

        if from_currency and to_currency and transaction_type:
            if transaction_type.is_exchange and from_currency == to_currency:
                errors['to_currency'] = _('To currency must be different from from currency for exchange transactions')

        # Validate exchange rate calculation for exchange transactions
        exchange_rate = data.get('exchange_rate')
        if (transaction_type and transaction_type.is_exchange and
            from_amount and to_amount and exchange_rate):
            calculated_rate = to_amount / from_amount
            rate_difference = abs(calculated_rate - exchange_rate)

            # Allow small rounding differences
            if rate_difference > Decimal('0.000001'):
                errors['exchange_rate'] = _('Exchange rate does not match the amount calculation')

        # Validate multi-step transaction data
        parent_transaction = data.get('parent_transaction')
        step_number = data.get('step_number')
        total_steps = data.get('total_steps')

        if parent_transaction:
            if step_number and total_steps and step_number > total_steps:
                errors['step_number'] = _('Step number cannot be greater than total steps')

        # Handle courier field validation based on frontend behavior
        delivery_method = data.get('delivery_method')
        courier = data.get('courier')
        courier_name = data.get('courier_name')
        courier_type = data.get('courier_type')

        # Frontend hardcodes delivery_method to 'cash' for deposits/withdrawals but may still send courier field
        # We need to clean up courier fields when delivery method is not 'courier'
        if delivery_method != Transaction.DeliveryMethod.COURIER:
            # Clear courier fields when delivery method is not courier
            if courier:
                data['courier'] = None
            if courier_name:
                data['courier_name'] = None
            if courier_type:
                data['courier_type'] = None
        else:
            # Only validate courier when delivery method is actually 'courier'
            if not courier and not courier_name:
                errors['courier'] = _('Courier is required when delivery method is courier')
            elif courier_name and courier_type == 'customer':
                # Handle customer courier - store the name in notes
                data['courier'] = None
                existing_notes = data.get('notes', '')
                courier_note = f"Customer Courier: {courier_name}"
                if existing_notes:
                    data['notes'] = f"{existing_notes}\n{courier_note}"
                else:
                    data['notes'] = courier_note
            elif courier and not self._is_valid_uuid(courier):
                # If courier is provided but not a valid UUID, treat as customer courier name
                data['courier_name'] = courier
                data['courier_type'] = 'customer'
                data['courier'] = None
                existing_notes = data.get('notes', '')
                courier_note = f"Customer Courier: {courier}"
                if existing_notes:
                    data['notes'] = f"{existing_notes}\n{courier_note}"
                else:
                    data['notes'] = courier_note

        # Validate location is provided
        location = data.get('location')
        if not location:
            # Try to get from request user
            request = self.context.get('request')
            if request and hasattr(request, 'user') and request.user.location:
                data['location'] = request.user.location
            else:
                errors['location'] = _('Location is required')

        # Handle special field mappings
        # Map internal/external reference to reference_number if not already set
        if not data.get('reference_number'):
            if data.get('internal_reference'):
                data['reference_number'] = data['internal_reference']
            elif data.get('external_reference'):
                data['reference_number'] = data['external_reference']

        # Handle courier field conversion to UUID if needed
        self._process_courier_field_conversion(data)

        if errors:
            raise serializers.ValidationError(errors)

        return data



    def _process_courier_field_conversion(self, data):
        """Convert courier field to proper UUID or None based on validation results."""
        courier_value = data.get('courier')

        if courier_value and self._is_valid_uuid(courier_value):
            # It's a valid UUID, try to convert to User instance
            try:
                from apps.accounts.models import User
                courier_user = User.objects.get(id=courier_value)
                data['courier'] = courier_user
            except User.DoesNotExist:
                # Invalid UUID reference, clear it
                data['courier'] = None
        else:
            # Not a valid UUID or empty, set to None (customer courier names are handled in notes)
            data['courier'] = None
    
    def create(self, validated_data):
        """Create a new transaction with automatic commission calculation and retry logic."""
        import time
        from django.db.utils import OperationalError

        max_retries = 3
        retry_delay = 0.2

        for attempt in range(max_retries):
            try:
                with transaction.atomic():
                    # Auto-calculate commission if not provided
                    if 'commission_amount' not in validated_data or validated_data.get('commission_amount', 0) == 0:
                        # Create temporary transaction for commission calculation
                        temp_transaction_data = {
                            'location': validated_data.get('location'),
                            'transaction_type': validated_data.get('transaction_type'),
                            'from_currency': validated_data.get('from_currency'),
                            'to_currency': validated_data.get('to_currency'),
                            'from_amount': validated_data.get('from_amount'),
                            'delivery_method': validated_data.get('delivery_method')
                        }

                        commission_result = commission_calculator.get_commission_preview(temp_transaction_data)

                        if commission_result['amount'] > 0:
                            validated_data['commission_amount'] = commission_result['amount']
                            validated_data['commission_currency'] = commission_result['currency']

                            logger.info(f"Auto-calculated commission: {commission_result['currency'].format_amount_with_symbol(commission_result['amount'])}")

                    # Filter out non-model fields before creating transaction
                    non_model_fields = {'document_files', 'document_type', 'transaction_type_code'}
                    transaction_data = {k: v for k, v in validated_data.items() if k not in non_model_fields}

                    # Create the transaction
                    transaction_obj = Transaction.objects.create(**transaction_data)

                    logger.info(f"Transaction created in serializer: {transaction_obj.transaction_number} (attempt {attempt + 1})")
                    break  # Success, exit retry loop

            except OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    logger.warning(f"Database locked in serializer create, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 1.5  # Moderate exponential backoff
                    continue
                else:
                    logger.error(f"Database lock persisted in serializer after {max_retries} attempts: {str(e)}")
                    raise serializers.ValidationError({
                        'non_field_errors': ['Database is temporarily busy. Please try again in a moment.']
                    })
            except Exception as e:
                logger.error(f"Transaction creation failed in serializer (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 1.5
                    continue
                else:
                    raise

        # Log the creation (handle cases where there's no request context)
        if 'request' in self.context:
            log_user_action(
                user=self.context['request'].user,
                action='create_transaction',
                model_name='Transaction',
                object_id=str(transaction_obj.id),
                object_repr=str(transaction_obj),
                ip_address=get_client_ip(self.context['request']),
                additional_data={
                    'transaction_number': transaction_obj.transaction_number,
                    'customer': transaction_obj.customer.get_display_name(),
                    'amount': str(transaction_obj.from_amount),
                    'currency': transaction_obj.from_currency.code,
                    'commission': str(transaction_obj.commission_amount) if transaction_obj.commission_amount else '0'
                }
            )
            logger.info(f"Transaction created: {transaction_obj.transaction_number} by {self.context['request'].user}")
        else:
            logger.info(f"Transaction created: {transaction_obj.transaction_number} (system/test)")

        return transaction_obj

    def validate_commission_amount(self, value):
        """Validate commission amount."""
        from decimal import Decimal

        # Handle empty string or None values
        if value == '' or value is None:
            return Decimal('0.00')

        # Convert to Decimal if it's a string
        if isinstance(value, str):
            try:
                value = Decimal(value)
            except (ValueError, TypeError):
                raise serializers.ValidationError("Commission amount must be a valid number")

        if value < 0:
            raise serializers.ValidationError("Commission amount cannot be negative")
        return value


class TransactionSerializer(serializers.ModelSerializer):
    """Detailed serializer for Transaction model."""

    customer = CustomerListSerializer(read_only=True)
    location = LocationListSerializer(read_only=True)
    transaction_type = TransactionTypeSerializer(read_only=True)
    from_currency = CurrencyListSerializer(read_only=True)
    to_currency = CurrencyListSerializer(read_only=True)
    commission_currency = CurrencyListSerializer(read_only=True)

    # Display fields
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    delivery_method_display = serializers.CharField(source='get_delivery_method_display', read_only=True)
    display_amount = serializers.CharField(source='get_display_amount', read_only=True)
    formatted_commission = serializers.SerializerMethodField()

    # Calculated fields
    is_exchange_transaction = serializers.BooleanField(read_only=True)
    is_multi_step = serializers.BooleanField(read_only=True)
    is_completed = serializers.BooleanField(read_only=True)
    can_be_approved = serializers.BooleanField(read_only=True)
    can_be_cancelled = serializers.BooleanField(read_only=True)
    profit = serializers.SerializerMethodField()

    # Related data
    balance_entries = BalanceEntrySerializer(many=True, read_only=True)
    child_transactions = TransactionListSerializer(many=True, read_only=True)

    class Meta:
        model = Transaction
        fields = [
            'id', 'transaction_number', 'transaction_type', 'customer', 'location',
            'description', 'reference_number', 'from_currency', 'to_currency',
            'from_amount', 'to_amount', 'exchange_rate', 'commission_amount',
            'commission_currency', 'status', 'status_display', 'delivery_method',
            'delivery_method_display', 'courier', 'delivery_address', 'tracking_code',
            'approved_by', 'approved_at',
            'completed_at', 'parent_transaction', 'step_number', 'total_steps',
            'notes', 'display_amount', 'formatted_commission', 'is_exchange_transaction',
            'is_multi_step', 'is_completed', 'can_be_approved', 'can_be_cancelled',
            'profit', 'balance_entries', 'child_transactions', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'transaction_number', 'approved_by', 'approved_at',
            'completed_at', 'created_at', 'updated_at'
        ]

    def get_formatted_commission(self, obj):
        """Return formatted commission amount."""
        if obj.commission_amount and obj.commission_currency:
            return obj.commission_currency.format_amount_with_symbol(obj.commission_amount)
        return None

    def get_profit(self, obj):
        """Return calculated profit."""
        profit = obj.calculate_profit()
        if profit and obj.commission_currency:
            return obj.commission_currency.format_amount_with_symbol(profit)
        return None


class TransactionUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating transactions."""

    class Meta:
        model = Transaction
        fields = [
            'description', 'reference_number', 'delivery_method',
            'courier', 'delivery_address', 'tracking_code', 'notes'
        ]

    def update(self, instance, validated_data):
        """Update transaction with logging."""
        with transaction.atomic():
            # Store old values for logging
            old_values = {
                'description': instance.description,
                'delivery_method': instance.delivery_method,
                'notes': instance.notes
            }

            # Update the instance
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            # Log the update
            log_user_action(
                user=self.context['request'].user,
                action='update_transaction',
                model_name='Transaction',
                object_id=str(instance.id),
                object_repr=str(instance),
                ip_address=get_client_ip(self.context['request']),
                additional_data={
                    'transaction_number': instance.transaction_number,
                    'old_values': old_values,
                    'new_values': validated_data
                }
            )

            logger.info(f"Transaction updated: {instance.transaction_number} by {self.context['request'].user}")

            return instance


class TransactionStatusSerializer(serializers.Serializer):
    """Serializer for transaction status changes."""

    status = serializers.ChoiceField(choices=Transaction.Status.choices)
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate_status(self, value):
        """Validate status transition."""
        instance = self.instance
        current_status = instance.status

        # Define valid status transitions
        valid_transitions = {
            Transaction.Status.DRAFT: [Transaction.Status.PENDING, Transaction.Status.APPROVED, Transaction.Status.CANCELLED],  # Allow direct approval from draft
            Transaction.Status.PENDING: [Transaction.Status.APPROVED, Transaction.Status.REJECTED, Transaction.Status.CANCELLED],
            Transaction.Status.APPROVED: [Transaction.Status.COMPLETED, Transaction.Status.CANCELLED],
            Transaction.Status.COMPLETED: [],  # Completed transactions cannot be changed
            Transaction.Status.CANCELLED: [],  # Cancelled transactions cannot be changed
            Transaction.Status.REJECTED: [Transaction.Status.PENDING],  # Can be resubmitted
        }

        if value not in valid_transitions.get(current_status, []):
            raise serializers.ValidationError(
                f"Cannot change status from {current_status} to {value}"
            )

        return value


class TransactionStatsSerializer(serializers.Serializer):
    """Serializer for transaction statistics."""

    total_transactions = serializers.IntegerField()
    pending_transactions = serializers.IntegerField()
    completed_transactions = serializers.IntegerField()
    cancelled_transactions = serializers.IntegerField()
    transactions_by_status = serializers.DictField()
    transactions_by_location = serializers.DictField()
    transactions_by_currency = serializers.DictField()
    total_volume_today = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_commission_today = serializers.DecimalField(max_digits=15, decimal_places=2)
    average_transaction_amount = serializers.DecimalField(max_digits=15, decimal_places=2)


class BalanceSummarySerializer(serializers.Serializer):
    """Serializer for balance summary data."""

    customer_balances = serializers.ListField()
    company_balances = serializers.ListField()
    total_balances_by_currency = serializers.DictField()
    locations = serializers.ListField()
    currencies = serializers.ListField()


class CustomerBalanceSerializer(serializers.Serializer):
    """Serializer for customer balance information."""

    customer = serializers.CharField()
    customer_id = serializers.UUIDField()
    location = serializers.CharField()
    location_id = serializers.UUIDField()
    currency = serializers.CharField()
    currency_id = serializers.UUIDField()
    currency_symbol = serializers.CharField()
    balance = serializers.DecimalField(max_digits=15, decimal_places=6)
    formatted_balance = serializers.CharField()
    last_updated = serializers.DateTimeField()


class CompanyBalanceSerializer(serializers.Serializer):
    """Serializer for company balance information."""

    location = serializers.CharField()
    location_id = serializers.UUIDField()
    currency = serializers.CharField()
    currency_id = serializers.UUIDField()
    currency_symbol = serializers.CharField()
    balance = serializers.DecimalField(max_digits=15, decimal_places=6)
    formatted_balance = serializers.CharField()
    last_updated = serializers.DateTimeField()


class BalanceCalculationSerializer(serializers.Serializer):
    """Serializer for balance calculation requests."""

    customer_id = serializers.UUIDField(required=False)
    location_id = serializers.UUIDField(required=False)
    currency_code = serializers.CharField(max_length=3, required=False)
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)

    def validate_currency_code(self, value):
        """Validate currency code exists."""
        if value:
            from apps.currencies.models import Currency
            if not Currency.objects.filter(code=value, is_active=True).exists():
                raise serializers.ValidationError(f"Currency {value} not found or inactive")
        return value


class TransactionDocumentSerializer(serializers.ModelSerializer):
    """Serializer for TransactionDocument model."""

    file_size_formatted = serializers.CharField(source='get_file_size_formatted', read_only=True)
    file_extension = serializers.CharField(source='get_file_extension', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_display_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_display_name', read_only=True)
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True)

    class Meta:
        model = TransactionDocument
        fields = [
            'id', 'transaction', 'document_type', 'document_type_display', 'title',
            'file', 'file_size', 'file_size_formatted', 'file_extension', 'mime_type',
            'uploaded_by', 'uploaded_by_name', 'is_required', 'is_verified',
            'verified_by', 'verified_by_name', 'verified_at', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'file_size', 'file_size_formatted', 'file_extension', 'mime_type',
            'uploaded_by', 'uploaded_by_name', 'verified_by', 'verified_by_name',
            'verified_at', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        """Create document with uploaded_by user."""
        validated_data['uploaded_by'] = self.context['request'].user
        return super().create(validated_data)


class TransactionDocumentUploadSerializer(serializers.ModelSerializer):
    """Serializer for uploading transaction documents."""

    class Meta:
        model = TransactionDocument
        fields = ['transaction', 'document_type', 'title', 'file', 'is_required', 'notes']

    def validate_file(self, value):
        """Validate uploaded file."""
        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if value.size > max_size:
            raise serializers.ValidationError(
                _('File size cannot exceed 10MB')
            )

        # Check file extension
        allowed_extensions = [
            'pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx',
            'xls', 'xlsx', 'txt', 'csv'
        ]

        file_extension = value.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            raise serializers.ValidationError(
                _('File type not allowed. Allowed types: %(types)s') % {
                    'types': ', '.join(allowed_extensions)
                }
            )

        return value

    def create(self, validated_data):
        """Create document with uploaded_by user."""
        validated_data['uploaded_by'] = self.context['request'].user
        return super().create(validated_data)


class BulkDocumentUploadSerializer(serializers.Serializer):
    """Serializer for bulk document upload."""

    transaction_id = serializers.UUIDField()
    document_type = serializers.ChoiceField(choices=TransactionDocument.DocumentType.choices)
    files = serializers.ListField(
        child=serializers.FileField(),
        min_length=1,
        max_length=10,  # Maximum 10 files per upload
        help_text="List of files to upload (max 10 files)"
    )

    def validate_files(self, files):
        """Validate uploaded files."""
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"Validating {len(files)} files")
        for i, file in enumerate(files):
            logger.info(f"File {i}: {file.name if hasattr(file, 'name') else 'No name'} - Type: {type(file)}")
            if hasattr(file, 'size'):
                logger.info(f"File {i} size: {file.size}")
            if hasattr(file, 'content_type'):
                logger.info(f"File {i} content_type: {file.content_type}")

        # Check total size
        total_size = sum(file.size for file in files)
        max_total_size = 50 * 1024 * 1024  # 50MB total

        if total_size > max_total_size:
            raise serializers.ValidationError(
                f"Total file size cannot exceed 50MB. Current total: {total_size / 1024 / 1024:.2f}MB"
            )

        # Validate each file
        allowed_extensions = [
            'pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx',
            'xls', 'xlsx', 'txt', 'csv'
        ]

        for file in files:
            # Check individual file size (max 10MB)
            max_size = 10 * 1024 * 1024  # 10MB
            if file.size > max_size:
                raise serializers.ValidationError(
                    f"File '{file.name}' size cannot exceed 10MB"
                )

            # Check file extension
            file_extension = file.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                raise serializers.ValidationError(
                    f"File '{file.name}' type not allowed. Allowed types: {', '.join(allowed_extensions)}"
                )

        return files

    def validate_transaction_id(self, value):
        """Validate transaction exists and user has access."""
        try:
            transaction = Transaction.objects.get(id=value, is_deleted=False)

            # Check if user has access to this transaction
            request = self.context.get('request')
            if request and hasattr(request, 'user'):
                user = request.user
                if not user.can_manage_users() and user.location != transaction.location:
                    raise serializers.ValidationError(
                        "You do not have permission to upload documents for this transaction"
                    )

            return value
        except Transaction.DoesNotExist:
            raise serializers.ValidationError("Transaction not found")


class CommissionTierSerializer(serializers.ModelSerializer):
    """Serializer for commission tiers."""

    class Meta:
        model = CommissionTier
        fields = [
            'id', 'min_amount', 'max_amount', 'percentage_rate',
            'fixed_amount', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CommissionRuleSerializer(serializers.ModelSerializer):
    """Serializer for commission rules."""

    tiers = CommissionTierSerializer(many=True, read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    transaction_type_name = serializers.CharField(source='transaction_type.name', read_only=True)
    from_currency_code = serializers.CharField(source='from_currency.code', read_only=True)
    to_currency_code = serializers.CharField(source='to_currency.code', read_only=True)
    commission_currency_code = serializers.CharField(source='commission_currency.code', read_only=True)

    class Meta:
        model = CommissionRule
        fields = [
            'id', 'name', 'description', 'location', 'location_name',
            'transaction_type', 'transaction_type_name', 'applicable_for',
            'from_currency', 'from_currency_code', 'to_currency', 'to_currency_code',
            'min_amount', 'max_amount', 'commission_type', 'percentage_rate',
            'fixed_amount', 'commission_currency', 'commission_currency_code',
            'is_active', 'priority', 'effective_from', 'effective_until',
            'tiers', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CommissionRuleListSerializer(serializers.ModelSerializer):
    """Simplified serializer for commission rule lists."""

    location_name = serializers.CharField(source='location.name', read_only=True)
    transaction_type_name = serializers.CharField(source='transaction_type.name', read_only=True)
    commission_type_display = serializers.CharField(source='get_commission_type_display', read_only=True)
    applicable_for_display = serializers.CharField(source='get_applicable_for_display', read_only=True)

    class Meta:
        model = CommissionRule
        fields = [
            'id', 'name', 'location_name', 'transaction_type_name',
            'commission_type', 'commission_type_display', 'applicable_for',
            'applicable_for_display', 'percentage_rate', 'fixed_amount',
            'is_active', 'priority', 'created_at'
        ]


class CommissionCalculationSerializer(serializers.Serializer):
    """Serializer for commission calculation requests."""

    location = serializers.UUIDField()
    transaction_type = serializers.UUIDField(required=False, allow_null=True)
    from_currency = serializers.UUIDField()
    to_currency = serializers.UUIDField()
    from_amount = serializers.DecimalField(max_digits=15, decimal_places=6)
    delivery_method = serializers.CharField(required=False, allow_blank=True)

    def validate_from_amount(self, value):
        """Validate transaction amount."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        return value


class CommissionCalculationResultSerializer(serializers.Serializer):
    """Serializer for commission calculation results."""

    amount = serializers.DecimalField(max_digits=15, decimal_places=6)
    currency_code = serializers.CharField()
    formatted_amount = serializers.CharField()
    rule_name = serializers.CharField(allow_null=True)
    rule_type = serializers.CharField(allow_null=True)
    breakdown = serializers.DictField()

    def to_representation(self, instance):
        """Convert commission calculation result to serialized format."""
        if isinstance(instance, dict):
            currency = instance.get('currency')
            amount = instance.get('amount', 0)

            # Handle currency object properly
            if currency:
                if hasattr(currency, 'code'):
                    # It's a Currency object
                    currency_code = currency.code
                    formatted_amount = currency.format_amount_with_symbol(amount)
                else:
                    # It's a UUID, need to fetch the Currency object
                    from apps.currencies.models import Currency
                    try:
                        currency_obj = Currency.objects.get(id=currency)
                        currency_code = currency_obj.code
                        formatted_amount = currency_obj.format_amount_with_symbol(amount)
                    except Currency.DoesNotExist:
                        currency_code = 'N/A'
                        formatted_amount = 'N/A'
            else:
                currency_code = 'N/A'
                formatted_amount = 'N/A'

            return {
                'amount': amount,
                'currency_code': currency_code,
                'formatted_amount': formatted_amount,
                'rule_name': instance.get('rule').name if instance.get('rule') else None,
                'rule_type': instance.get('rule').get_commission_type_display() if instance.get('rule') else None,
                'breakdown': instance.get('breakdown', {})
            }
        return instance
