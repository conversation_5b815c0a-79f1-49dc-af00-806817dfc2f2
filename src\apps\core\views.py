"""
API views for Arena Doviz Core app.
Provides dashboard data, analytics endpoints, and production monitoring.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Sum, Q, Avg
from django.utils import timezone
from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from datetime import timedelta, datetime
from decimal import Decimal
from apps.transactions.models import Transaction, BalanceEntry
from apps.customers.models import Customer
from apps.currencies.models import Currency
from apps.locations.models import Location
import logging

logger = logging.getLogger(__name__)


class DashboardViewSet(viewsets.ViewSet):
    """ViewSet for dashboard data and analytics."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get comprehensive dashboard statistics."""
        try:
            # Get date range for comparisons
            today = timezone.now().date()
            yesterday = today - timedelta(days=1)
            last_week = today - timedelta(days=7)

            # Filter by user location if not admin
            location_filter = Q()
            if not request.user.can_manage_users() and request.user.location:
                location_filter = Q(location=request.user.location)

            # Get today's statistics
            today_transactions = Transaction.objects.filter(
                created_at__date=today,
                is_deleted=False
            ).filter(location_filter)

            yesterday_transactions = Transaction.objects.filter(
                created_at__date=yesterday,
                is_deleted=False
            ).filter(location_filter)

            # Calculate metrics
            today_count = today_transactions.count()
            yesterday_count = yesterday_transactions.count()

            today_commission = sum(float(t.commission_amount or 0) for t in today_transactions)
            yesterday_commission = sum(float(t.commission_amount or 0) for t in yesterday_transactions)

            pending_count = Transaction.objects.filter(
                status=Transaction.Status.PENDING,
                is_deleted=False
            ).filter(location_filter).count()

            # Get customer count
            total_customers = Customer.objects.filter(is_deleted=False).count()
            last_week_customers = Customer.objects.filter(
                created_at__date__gte=last_week,
                is_deleted=False
            ).count()

            # Get balance data for major currencies
            balance_data = self._get_balance_summary(location_filter)

            # Calculate percentage changes
            def calculate_change(current, previous):
                if previous == 0:
                    return "+100%" if current > 0 else "0%"
                change = ((current - previous) / previous) * 100
                return f"{'+' if change >= 0 else ''}{change:.1f}%"

            return Response({
                'total_customers': total_customers,
                'customers_change': f"+{last_week_customers} this week",
                'today_transactions': today_count,
                'transactions_change': calculate_change(today_count, yesterday_count),
                'pending_approvals': pending_count,
                'approvals_change': f"{pending_count} pending",
                'today_commission': f"${today_commission:.2f}",
                'commission_change': calculate_change(today_commission, yesterday_commission),
                'balances': balance_data
            })

        except Exception as e:
            logger.error(f"Error getting dashboard stats: {str(e)}")
            return Response(
                {'error': 'Failed to load dashboard statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def chart_data(self, request):
        """Get comprehensive chart data for dashboard with filtering support."""
        try:
            # Get date range from query parameters
            days = int(request.query_params.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)

            # Build location filter
            location_filter = Q()

            # Check for explicit location filter from frontend
            location_param = request.query_params.get('location')
            if location_param:
                # Use the specific location filter from frontend
                location_filter = Q(location_id=location_param)
            elif not request.user.can_manage_users() and request.user.location:
                # Default to user's location if not admin and no specific filter
                location_filter = Q(location=request.user.location)

            # Build currency filter
            currency_filter = Q()
            currency_param = request.query_params.get('currency')
            if currency_param:
                # Filter by specific currency (either from_currency or to_currency)
                currency_filter = Q(from_currency_id=currency_param) | Q(to_currency_id=currency_param)
            
            # Combine filters for data queries
            combined_filter = location_filter & currency_filter

            # Get transaction volume data
            transaction_volume = self._get_transaction_volume_data(start_date, end_date, combined_filter)

            # Get currency distribution data
            currency_distribution = self._get_currency_distribution_data(start_date, end_date, combined_filter)

            # Get profit analysis data
            profit_analysis = self._get_profit_analysis_data(start_date, end_date, combined_filter)

            # Get balance trends data
            balance_trends = self._get_balance_trends_data(start_date, end_date, combined_filter)

            # Get transaction status distribution
            status_distribution = self._get_status_distribution_data(start_date, end_date, combined_filter)

            # Get location performance data (for admins) - only apply currency filter, not location filter
            location_performance = None
            if request.user.can_manage_users():
                location_performance = self._get_location_performance_data(start_date, end_date, currency_filter)
            
            return Response({
                'transaction_volume': transaction_volume,
                'currency_distribution': currency_distribution,
                'profit_analysis': profit_analysis,
                'balance_trends': balance_trends,
                'status_distribution': status_distribution,
                'location_performance': location_performance,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting chart data: {str(e)}")
            return Response(
                {'error': 'Failed to load chart data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_data(self, request):
        """Export analytics data in CSV format."""
        try:
            from django.http import HttpResponse
            import csv
            from io import StringIO

            # Get date range from query parameters
            days = int(request.query_params.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)

            # Build location filter
            location_filter = Q()
            location_param = request.query_params.get('location')
            if location_param:
                location_filter = Q(location_id=location_param)
            elif not request.user.can_manage_users() and request.user.location:
                location_filter = Q(location=request.user.location)

            # Build currency filter
            currency_filter = Q()
            currency_param = request.query_params.get('currency')
            if currency_param:
                currency_filter = Q(from_currency_id=currency_param) | Q(to_currency_id=currency_param)

            # Combine filters
            combined_filter = location_filter & currency_filter

            # Get transaction data with filters
            transactions = Transaction.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date,
                is_deleted=False
            ).filter(combined_filter).select_related(
                'customer', 'location', 'from_currency', 'to_currency', 'transaction_type'
            ).order_by('-created_at')

            # Create CSV response
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="arena_doviz_analytics_{start_date}_{end_date}.csv"'

            writer = csv.writer(response)

            # Write header
            writer.writerow([
                'Transaction Number',
                'Date',
                'Customer',
                'Location',
                'Type',
                'From Currency',
                'To Currency',
                'From Amount',
                'To Amount',
                'Exchange Rate',
                'Commission Amount',
                'Status',
                'Created By'
            ])

            # Write data
            for transaction in transactions:
                writer.writerow([
                    transaction.transaction_number,
                    transaction.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    transaction.customer.get_display_name() if transaction.customer else 'N/A',
                    transaction.location.name if transaction.location else 'N/A',
                    transaction.transaction_type.name if transaction.transaction_type else 'N/A',
                    transaction.from_currency.code if transaction.from_currency else 'N/A',
                    transaction.to_currency.code if transaction.to_currency else 'N/A',
                    str(transaction.from_amount),
                    str(transaction.to_amount),
                    str(transaction.exchange_rate),
                    str(transaction.commission_amount or 0),
                    transaction.get_status_display(),
                    transaction.created_by.username if transaction.created_by else 'N/A'
                ])

            logger.info(f"Analytics data exported by user: {request.user.username}")

            return response

        except Exception as e:
            logger.error(f"Error exporting analytics data: {str(e)}")
            return Response(
                {'error': 'Failed to export data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_transaction_volume_data(self, start_date, end_date, location_filter):
        """Get daily transaction volume data."""
        transactions = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            is_deleted=False
        ).filter(location_filter)
        
        # Group by date
        daily_data = {}
        current_date = start_date
        while current_date <= end_date:
            daily_data[current_date.isoformat()] = {
                'date': current_date.isoformat(),
                'count': 0,
                'volume': 0,
                'commission': 0
            }
            current_date += timedelta(days=1)
        
        # Aggregate transaction data
        for transaction in transactions:
            date_key = transaction.created_at.date().isoformat()
            if date_key in daily_data:
                daily_data[date_key]['count'] += 1
                daily_data[date_key]['volume'] += float(transaction.from_amount)
                daily_data[date_key]['commission'] += float(transaction.commission_amount or 0)
        
        return {
            'labels': list(daily_data.keys()),
            'datasets': [
                {
                    'label': 'Transaction Count',
                    'data': [data['count'] for data in daily_data.values()],
                    'type': 'line',
                    'borderColor': 'rgb(75, 192, 192)',
                    'backgroundColor': 'rgba(75, 192, 192, 0.1)',
                    'yAxisID': 'y'
                },
                {
                    'label': 'Volume (USD)',
                    'data': [data['volume'] for data in daily_data.values()],
                    'type': 'bar',
                    'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                    'borderColor': 'rgb(54, 162, 235)',
                    'yAxisID': 'y1'
                }
            ]
        }
    
    def _get_currency_distribution_data(self, start_date, end_date, location_filter):
        """Get currency distribution data."""
        currency_data = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        ).filter(location_filter).values(
            'from_currency__code', 'from_currency__name'
        ).annotate(
            count=Count('id'),
            total_amount=Sum('from_amount')
        ).order_by('-total_amount')
        
        labels = []
        data = []
        colors = [
            'rgb(255, 99, 132)',
            'rgb(54, 162, 235)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(153, 102, 255)',
            'rgb(255, 159, 64)'
        ]
        
        for i, item in enumerate(currency_data):
            labels.append(item['from_currency__code'])
            data.append(float(item['total_amount']))
        
        return {
            'labels': labels,
            'datasets': [{
                'data': data,
                'backgroundColor': colors[:len(data)],
                'borderWidth': 2
            }]
        }
    
    def _get_profit_analysis_data(self, start_date, end_date, location_filter):
        """Get profit analysis data."""
        transactions = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        ).filter(location_filter)
        
        # Group by date for profit trends
        daily_profit = {}
        current_date = start_date
        while current_date <= end_date:
            daily_profit[current_date.isoformat()] = 0
            current_date += timedelta(days=1)
        
        total_commission = 0
        for transaction in transactions:
            date_key = transaction.created_at.date().isoformat()
            commission = float(transaction.commission_amount or 0)
            if date_key in daily_profit:
                daily_profit[date_key] += commission
            total_commission += commission
        
        return {
            'labels': list(daily_profit.keys()),
            'datasets': [{
                'label': 'Daily Commission',
                'data': list(daily_profit.values()),
                'borderColor': 'rgb(255, 99, 132)',
                'backgroundColor': 'rgba(255, 99, 132, 0.1)',
                'fill': True
            }],
            'total_commission': total_commission
        }
    
    def _get_balance_trends_data(self, start_date, end_date, location_filter):
        """Get balance trends data."""
        # Get balance data for major currencies
        currencies = Currency.objects.filter(is_active=True)[:3]  # Top 3 currencies
        
        datasets = []
        colors = ['rgb(75, 192, 192)', 'rgb(255, 99, 132)', 'rgb(255, 205, 86)']
        
        for i, currency in enumerate(currencies):
            # Get daily balance snapshots
            daily_balances = {}
            current_date = start_date
            while current_date <= end_date:
                # Calculate balance up to this date
                balance = BalanceEntry.get_current_balance(
                    customer=None,  # Company balance
                    location=None,  # All locations for admin, filtered for others
                    currency=currency
                )
                daily_balances[current_date.isoformat()] = float(balance)
                current_date += timedelta(days=1)
            
            datasets.append({
                'label': f'{currency.code} Balance',
                'data': list(daily_balances.values()),
                'borderColor': colors[i % len(colors)],
                'backgroundColor': colors[i % len(colors)].replace('rgb', 'rgba').replace(')', ', 0.1)'),
                'fill': False
            })
        
        return {
            'labels': list(daily_balances.keys()) if currencies else [],
            'datasets': datasets
        }
    
    def _get_status_distribution_data(self, start_date, end_date, location_filter):
        """Get transaction status distribution data."""
        status_data = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            is_deleted=False
        ).filter(location_filter).values('status').annotate(
            count=Count('id')
        ).order_by('-count')
        
        labels = []
        data = []
        colors = {
            'completed': 'rgb(40, 167, 69)',
            'pending': 'rgb(255, 193, 7)',
            'approved': 'rgb(0, 123, 255)',
            'draft': 'rgb(108, 117, 125)',
            'cancelled': 'rgb(220, 53, 69)',
            'rejected': 'rgb(220, 53, 69)'
        }
        
        for item in status_data:
            status_display = dict(Transaction.Status.choices)[item['status']]
            labels.append(status_display)
            data.append(item['count'])
        
        return {
            'labels': labels,
            'datasets': [{
                'data': data,
                'backgroundColor': [colors.get(status, 'rgb(108, 117, 125)') for status in [item['status'] for item in status_data]],
                'borderWidth': 2
            }]
        }
    
    def _get_location_performance_data(self, start_date, end_date, additional_filter=None):
        """Get location performance data (admin only)."""
        base_filter = Q(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        )

        # Apply additional filter if provided (e.g., currency filter)
        if additional_filter:
            base_filter = base_filter & additional_filter

        location_data = Transaction.objects.filter(base_filter).values(
            'location__name', 'location__code'
        ).annotate(
            count=Count('id'),
            total_volume=Sum('from_amount'),
            total_commission=Sum('commission_amount')
        ).order_by('-total_volume')
        
        labels = []
        volume_data = []
        commission_data = []
        
        for item in location_data:
            labels.append(item['location__name'] or 'Unknown')
            volume_data.append(float(item['total_volume'] or 0))
            commission_data.append(float(item['total_commission'] or 0))
        
        return {
            'labels': labels,
            'datasets': [
                {
                    'label': 'Transaction Volume',
                    'data': volume_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                    'borderColor': 'rgb(54, 162, 235)',
                    'yAxisID': 'y'
                },
                {
                    'label': 'Commission Earned',
                    'data': commission_data,
                    'backgroundColor': 'rgba(255, 99, 132, 0.5)',
                    'borderColor': 'rgb(255, 99, 132)',
                    'yAxisID': 'y1'
                }
            ]
        }

    def _get_balance_summary(self, location_filter):
        """Get balance summary for major currencies."""
        try:
            # Get major currencies
            currencies = Currency.objects.filter(
                code__in=['USD', 'AED', 'IRR'],
                is_active=True
            ).order_by('code')

            balance_data = {}

            for currency in currencies:
                # Calculate company balance for this currency
                company_balance = BalanceEntry.objects.filter(
                    currency=currency,
                    customer__isnull=True,  # Company balance
                    is_deleted=False
                ).filter(location_filter).aggregate(
                    total=Sum('amount')
                )['total'] or 0

                # Get yesterday's balance for comparison
                yesterday = timezone.now().date() - timedelta(days=1)
                yesterday_balance = BalanceEntry.objects.filter(
                    currency=currency,
                    customer__isnull=True,
                    created_at__date__lte=yesterday,
                    is_deleted=False
                ).filter(location_filter).aggregate(
                    total=Sum('amount')
                )['total'] or 0

                # Calculate change
                change = float(company_balance) - float(yesterday_balance)
                change_text = f"{'+' if change >= 0 else ''}{currency.format_amount(change)}"

                balance_data[currency.code.lower()] = {
                    'balance': currency.format_amount_with_symbol(company_balance),
                    'change': change_text,
                    'raw_balance': float(company_balance)
                }

            return balance_data

        except Exception as e:
            logger.error(f"Error getting balance summary: {str(e)}")
            return {
                'usd': {'balance': '$0.00', 'change': '-', 'raw_balance': 0},
                'aed': {'balance': 'د.إ 0.00', 'change': '-', 'raw_balance': 0},
                'irr': {'balance': '﷼ 0', 'change': '-', 'raw_balance': 0}
            }


# Production Monitoring Views

@require_http_methods(["GET"])
def health_check(request):
    """Simple health check endpoint"""
    return JsonResponse({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': '1.0.0',
        'service': 'arena-doviz'
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_health_api(request):
    """Detailed system health check API"""
    try:
        # Try to get cached report first
        report = cache.get('system_health_report')

        if not report:
            # Generate new report if not cached
            from .monitoring import run_health_check
            report = run_health_check()

        return Response(report)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def production_monitoring_api(request):
    """Complete production monitoring API"""
    try:
        # Try to get cached report first
        report = cache.get('production_monitoring_report')

        if not report:
            # Generate new report if not cached
            from .monitoring import run_production_monitoring
            report = run_production_monitoring()

        return Response(report)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)

@login_required
def monitoring_dashboard(request):
    """Production monitoring dashboard"""
    return render(request, 'monitoring/dashboard.html', {
        'title': 'Arena Doviz Production Monitoring',
        'user': request.user
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def stat_arena_doviz(request):
    """Arena Doviz statistics endpoint for network access"""
    try:
        from apps.transactions.models import Transaction
        from apps.customers.models import Customer
        from apps.locations.models import Location
        from apps.currencies.models import Currency
        from django.db.models import Count, Sum
        from django.utils import timezone

        # Get basic statistics
        total_customers = Customer.objects.filter(is_deleted=False).count()
        total_transactions = Transaction.objects.filter(is_deleted=False).count()
        total_locations = Location.objects.filter(is_deleted=False).count()
        total_currencies = Currency.objects.filter(is_deleted=False).count()

        # Today's statistics
        today = timezone.now().date()
        today_transactions = Transaction.objects.filter(
            is_deleted=False,
            created_at__date=today
        ).count()

        # Pending approvals
        pending_approvals = Transaction.objects.filter(
            is_deleted=False,
            status=Transaction.Status.PENDING
        ).count()

        # Transaction volume today
        today_volume = Transaction.objects.filter(
            is_deleted=False,
            created_at__date=today,
            status=Transaction.Status.COMPLETED
        ).aggregate(total=Sum('from_amount'))['total'] or 0

        stats = {
            'status': 'success',
            'timestamp': timezone.now().isoformat(),
            'data': {
                'total_customers': total_customers,
                'total_transactions': total_transactions,
                'total_locations': total_locations,
                'total_currencies': total_currencies,
                'today_transactions': today_transactions,
                'pending_approvals': pending_approvals,
                'today_volume': float(today_volume),
                'system_status': 'operational'
            }
        }

        return Response(stats)

    except Exception as e:
        logger.error(f"Error in stat_arena_doviz: {e}")
        return Response({
            'status': 'error',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)


@login_required
def debug_auth(request):
    """Debug authentication page"""
    return render(request, 'debug_auth.html', {
        'title': 'Authentication Debug - Arena Doviz',
        'user': request.user
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_data_api(request):
    """API endpoint for dashboard data"""
    try:
        # Get both system health and production monitoring data
        system_report = cache.get('system_health_report')
        production_report = cache.get('production_monitoring_report')

        if not system_report:
            from .monitoring import run_health_check
            system_report = run_health_check()

        if not production_report:
            from .monitoring import run_production_monitoring
            production_report = run_production_monitoring()

        # Combine reports for dashboard
        dashboard_data = {
            'timestamp': production_report.get('timestamp', timezone.now().isoformat()),
            'overall_status': production_report.get('overall_status', 'UNKNOWN'),
            'system_health': system_report,
            'business_metrics': production_report.get('business_metrics', {}),
            'performance_metrics': production_report.get('performance_metrics', {}),
            'external_services': production_report.get('external_services', {}),
            'alerts': production_report.get('alerts', [])
        }

        return Response(dashboard_data)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def force_monitoring_refresh(request):
    """Force refresh of monitoring data"""
    try:
        # Clear cached reports
        cache.delete('system_health_report')
        cache.delete('production_monitoring_report')

        # Generate fresh reports
        from .monitoring import run_health_check, run_production_monitoring
        system_report = run_health_check()
        production_report = run_production_monitoring()

        return Response({
            'status': 'success',
            'message': 'Monitoring data refreshed',
            'timestamp': production_report.get('timestamp', timezone.now().isoformat())
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)
