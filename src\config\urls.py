"""
URL configuration for Arena Doviz Exchange Accounting System.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from django.http import HttpResponse
from django.views.i18n import JavaScriptCatalog
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
import logging

logger = logging.getLogger(__name__)

# Simple favicon handler
def favicon_view(request):
    return HttpResponse(status=204)  # No Content

# Main URL patterns
urlpatterns = [
    # Favicon
    path('favicon.ico', favicon_view, name='favicon'),

    # Login redirects for common URLs
    path('login/', RedirectView.as_view(url='/accounts/login/', permanent=True), name='login_redirect'),
    path('signin/', RedirectView.as_view(url='/accounts/login/', permanent=True), name='signin_redirect'),
    path('auth/login/', RedirectView.as_view(url='/accounts/login/', permanent=True), name='auth_login_redirect'),

    # Admin interface
    path('admin/', admin.site.urls),
    
    # API endpoints
    path('api/v1/accounts/', include('apps.accounts.urls')),
    path('api/v1/customers/', include('apps.customers.urls')),
    path('api/v1/locations/', include('apps.locations.urls')),
    path('api/v1/currencies/', include('apps.currencies.urls')),
    path('api/v1/transactions/', include('apps.transactions.urls')),
    path('api/v1/reports/', include('apps.reports.urls')),
    path('api/v1/core/', include('apps.core.urls')),

    # API documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # Internationalization
    path('i18n/', include('django.conf.urls.i18n')),
    path('jsi18n/', JavaScriptCatalog.as_view(), name='javascript-catalog'),

    # Production monitoring endpoints (using specific views to avoid namespace conflicts)
    path('health/', include('apps.core.urls', namespace='health')),
    path('monitoring/', include('apps.core.urls', namespace='monitoring')),
    path('api/v1/monitoring/', include('apps.core.urls', namespace='api_monitoring')),

    # Web interface
    path('', include('apps.core.web_urls')),
    path('accounts/', include('apps.accounts.web_urls')),
    path('users/', include('apps.accounts.web_urls')),  # Direct route for user management (same namespace as accounts)
    path('customers/', include('apps.customers.web_urls')),
    path('locations/', include('apps.locations.web_urls')),
    path('currencies/', include('apps.currencies.web_urls')),
    path('transactions/', include('apps.transactions.web_urls')),
    path('reports/', include('apps.reports.web_urls')),
]

# Serve static and media files
# In production, we need to serve static files through Django for testing
# In real production, use nginx or Apache to serve static files
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Debug toolbar for development
if settings.DEBUG:
    try:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
        logger.debug("Debug toolbar URLs added")
    except ImportError:
        logger.debug("Debug toolbar not available")
else:
    logger.debug("Debug toolbar disabled in production")

# Custom admin site configuration
admin.site.site_header = "Arena Doviz Exchange Accounting"
admin.site.site_title = "Arena Doviz Admin"
admin.site.index_title = "Welcome to Arena Doviz Administration"

# Log URL configuration loading
logger.info("Arena Doviz URL configuration loaded successfully")
