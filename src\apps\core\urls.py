"""
URL configuration for Arena Doviz Core app - Dashboard and Monitoring
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'core'

# API router for core endpoints
router = DefaultRouter()
router.register(r'dashboard', views.DashboardViewSet, basename='dashboard')

urlpatterns = [
    # API routes
    path('', include(router.urls)),

    # Health check endpoints
    path('health/', views.health_check, name='health_check'),
    path('health/system/', views.system_health_api, name='system_health_api'),

    # Production monitoring endpoints
    path('monitoring/production/', views.production_monitoring_api, name='production_monitoring_api'),
    path('monitoring/dashboard-data/', views.dashboard_data_api, name='dashboard_data_api'),
    path('monitoring/refresh/', views.force_monitoring_refresh, name='force_monitoring_refresh'),

    # Monitoring dashboard
    path('monitoring/', views.monitoring_dashboard, name='monitoring_dashboard'),

    # Arena Doviz statistics endpoint
    path('stat_arena_doviz/', views.stat_arena_doviz, name='stat_arena_doviz'),

    # Debug authentication page
    path('debug-auth/', views.debug_auth, name='debug_auth'),
]
