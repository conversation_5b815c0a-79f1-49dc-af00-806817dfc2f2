/**
 * Balance Adjustment Transaction Form Handler
 */

class AdjustmentTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'ADJUSTMENT';

        // Only initialize if form exists
        if (this.form) {
            this.init();
        } else {
            console.warn('Transaction form not found, skipping AdjustmentTransactionForm initialization');
        }
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
        this.setCurrentUser();
    }

    loadFormData() {
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
    }

    bindEvents() {
        $('#customer').on('change', () => {
            TransactionUtils.loadCustomerBalance().then(() => {
                this.updateCurrentBalance();
            });
        });

        $('#from_currency').on('change', () => {
            this.updateCurrentBalance();
        });

        $('#from_amount, #adjustment_type').on('change input', () => {
            this.calculateNewBalance();
            this.updateTransactionPreview();
        });

        $('#adjustment_reason').on('change', () => {
            this.toggleReasonDetails();
        });

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        $('#document_files').on('change', () => {
            TransactionUtils.handleFileSelection();
        });

        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validateAdjustmentForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateAdjustmentForm() {
        let isValid = true;
        const errors = [];

        const amount = parseFloat($('#from_amount').val());
        if (amount <= 0) {
            errors.push('Adjustment amount must be greater than zero');
            isValid = false;
        }

        if (!$('#adjustment_type').val()) {
            errors.push('Adjustment type is required');
            isValid = false;
        }

        if (!$('#adjustment_reason').val()) {
            errors.push('Adjustment reason is required');
            isValid = false;
        }

        if (!$('#adjustment_details').val()) {
            errors.push('Detailed explanation is required');
            isValid = false;
        }

        if (!$('#confirm_adjustment').is(':checked')) {
            errors.push('Confirmation is required for balance adjustments');
            isValid = false;
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const adjustmentType = data.results.find(type => type.code === 'ADJUSTMENT');
                if (adjustmentType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: adjustmentType.id
                    }).appendTo(this.form);
                }
            }
        });
    }

    // Removed - now using TransactionUtils.loadCustomers()

    // Removed - now using TransactionUtils.loadLocations()

    // Removed - now using TransactionUtils.loadCurrencies()

    // Removed - now using TransactionUtils.loadCustomerBalance()

    updateCurrentBalance() {
        const selectedCurrencyId = $('#from_currency').val();
        if (!selectedCurrencyId || !TransactionUtils.customerBalances) {
            $('#current_balance').val('');
            return;
        }

        // Get currency code from the selected option
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];

        // Find balance by currency code
        const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        if (balance) {
            $('#current_balance').val(balance.formatted_balance);
            this.currentBalance = balance.balance;
        } else {
            $('#current_balance').val('0.00');
            this.currentBalance = 0;
        }

        this.calculateNewBalance();
    }

    calculateNewBalance() {
        const adjustmentAmount = parseFloat($('#from_amount').val()) || 0;
        const adjustmentType = $('#adjustment_type').val();
        
        if (this.currentBalance !== undefined && adjustmentAmount > 0) {
            let newBalance;
            
            if (adjustmentType === 'increase') {
                newBalance = this.currentBalance + adjustmentAmount;
                $('#to_amount').val(adjustmentAmount);
            } else if (adjustmentType === 'decrease') {
                newBalance = this.currentBalance - adjustmentAmount;
                $('#to_amount').val(-adjustmentAmount);
            } else {
                $('#new_balance').val('');
                return;
            }
            
            $('#new_balance').val(newBalance.toFixed(2));
            
            // Show warning for negative balance
            const warningElement = $('#negative-balance-warning');
            if (newBalance < 0) {
                warningElement.show();
            } else {
                warningElement.hide();
            }
        } else {
            $('#new_balance').val('');
        }
    }

    toggleReasonDetails() {
        // This could be expanded to show additional fields based on reason
        console.log('Reason changed:', $('#adjustment_reason').val());
    }

    setCurrentUser() {
        $('#requested_by').val('Current User');
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            adjustmentType: $('#adjustment_type option:selected').text(),
            amount: formData.get('from_amount'),
            currentBalance: $('#current_balance').val(),
            newBalance: $('#new_balance').val(),
            reason: $('#adjustment_reason option:selected').text(),
            confirmed: $('#confirm_adjustment').is(':checked')
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>Customer:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Currency:</strong> ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.adjustmentType && preview.adjustmentType !== 'Select type...') {
            previewHtml += `<div class="list-group-item"><strong>Type:</strong> ${preview.adjustmentType}</div>`;
        }
        
        if (preview.amount) {
            previewHtml += `<div class="list-group-item"><strong>Amount:</strong> ${preview.amount}</div>`;
        }
        
        if (preview.currentBalance) {
            previewHtml += `<div class="list-group-item"><strong>Current Balance:</strong> ${preview.currentBalance}</div>`;
        }
        
        if (preview.newBalance) {
            const balanceClass = parseFloat(preview.newBalance) >= 0 ? 'text-success' : 'text-danger';
            previewHtml += `<div class="list-group-item"><strong>New Balance:</strong> <span class="${balanceClass}">${preview.newBalance}</span></div>`;
        }
        
        if (preview.reason && preview.reason !== 'Select reason...') {
            previewHtml += `<div class="list-group-item"><strong>Reason:</strong> ${preview.reason}</div>`;
        }
        
        if (preview.confirmed) {
            previewHtml += `<div class="list-group-item"><strong>Status:</strong> <span class="text-success">Confirmed</span></div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateAdjustmentForm()) {
            return;
        }

        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData.entries());
        
        // Adjustments always require approval
        data.status = 'pending';
        data.to_currency = data.from_currency;
        data.exchange_rate = 1;
        data.delivery_method = 'internal';

        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'POST',
            headers: this.getAuthHeaders(),
            data: JSON.stringify(data),
            success: (response) => {
                const transactionId = response.id;
                const files = $('#document_files')[0].files;
                if (files.length > 0) {
                    this.uploadDocuments(transactionId, files);
                } else {
                    TransactionUtils.showAlert('success', 'Balance adjustment created successfully and submitted for approval');
                    setTimeout(() => {
                        window.location.href = `/transactions/${transactionId}/`;
                    }, 2000);
                }
            },
            error: (xhr) => {
                const errors = xhr.responseJSON;
                let errorMessage = 'Error creating balance adjustment';
                if (errors) {
                    errorMessage += ':<br>';
                    for (let field in errors) {
                        errorMessage += `${field}: ${errors[field]}<br>`;
                    }
                }
                TransactionUtils.showAlert('danger', errorMessage);
            }
        });
    }

    uploadDocuments(transactionId, files) {
        const formData = new FormData();
        const documentType = $('#document_type').val();

        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        formData.append('document_type', documentType);
        formData.append('transaction', transactionId);

        $.ajax({
            url: '/api/v1/transactions/documents/',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: formData,
            processData: false,
            contentType: false,
            success: () => {
                TransactionUtils.showAlert('success', 'Balance adjustment and documents uploaded successfully');
                setTimeout(() => {
                    window.location.href = `/transactions/${transactionId}/`;
                }, 2000);
            },
            error: () => {
                TransactionUtils.showAlert('warning', 'Balance adjustment created but document upload failed');
                setTimeout(() => {
                    window.location.href = `/transactions/${transactionId}/`;
                }, 5000);
            }
        });
    }

    // Removed - now using TransactionUtils.handleFileSelection()

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    // Removed - now using TransactionUtils.showAlert()
}

$(document).ready(() => {
    new AdjustmentTransactionForm();
});
