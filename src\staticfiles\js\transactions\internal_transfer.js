/**
 * Internal Transfer Transaction Form Handler
 */

class InternalTransferForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'INTERNAL_TRANSFER';
        this.customerBalances = [];
        this.recipientDetails = null;
        this.recipientBalances = [];
        this.initialized = false; // Prevent multiple initializations

        // Only initialize if form exists and not already initialized
        if (this.form && !this.initialized) {
            this.init();
        } else if (this.initialized) {
            console.warn('InternalTransferForm already initialized, skipping');
        } else {
            console.warn('Transaction form not found, skipping InternalTransferForm initialization');
        }
    }

    init() {
        if (this.initialized) {
            console.log('InternalTransferForm already initialized, skipping');
            return;
        }

        console.log('InternalTransferForm.init() called');
        this.initialized = true; // Mark as initialized

        this.loadFormData();
        this.setupEventListeners();
        this.setupValidation();
        this.setCurrentUser();
    }

    loadFormData() {
        console.log('InternalTransferForm.loadFormData() called');
        this.loadTransactionTypes();

        // Wait for global TransactionUtils instance to be available
        const waitForTransactionUtils = () => {
            if (window.transactionUtils) {
                // Load customers first, then pre-populate after loading is complete
                Promise.all([
                    window.transactionUtils.loadCustomers(),
                    window.transactionUtils.loadLocations(),
                    window.transactionUtils.loadCurrencies()
                ]).then(() => {
                    // Pre-populate customer from URL parameter after all data is loaded
                    setTimeout(() => {
                        window.transactionUtils.prePopulateCustomerFromUrl();
                    }, 500); // Give time for dropdowns to be fully initialized
                }).catch(error => {
                    console.error('Error loading form data:', error);
                    // Still try to pre-populate even if some loading failed
                    setTimeout(() => {
                        window.transactionUtils.prePopulateCustomerFromUrl();
                    }, 1000);
                });
            } else {
                setTimeout(waitForTransactionUtils, 100);
            }
        };

        waitForTransactionUtils();
    }

    setupEventListeners() {
        // Customer changes
        $('#customer').on('change', () => {
            if (window.transactionUtils) {
                window.transactionUtils.loadCustomerBalance().then(() => {
                    // Update local reference to customer balances
                    this.customerBalances = TransactionUtils.customerBalances || [];
                    this.checkSufficientBalance();
                }).catch(err => {
                    console.error('Error loading customer balance:', err);
                });
            }
            this.updateRecipientOptions();
            this.updateTransactionPreview();
        });

        // Recipient customer changes
        $('#recipient_customer').on('change', () => {
            this.loadRecipientDetails();
            this.loadRecipientBalance();
            this.updateTransactionPreview();
        });

        // Amount changes
        $('#from_amount').on('input', () => {
            this.checkSufficientBalance();
            this.updateTransferSummary();
            this.updateTransactionPreview();
        });

        // Currency changes
        $('#from_currency').on('change', () => {
            // Ensure we have the latest customer balances
            this.customerBalances = TransactionUtils.customerBalances || [];
            this.checkSufficientBalance();
            this.updateTransferSummary();
            this.updateTransactionPreview();
        });

        // Commission changes
        $('#commission_amount').on('input', () => {
            this.updateTransferSummary();
            this.updateTransactionPreview();
        });
    }

    updateRecipientOptions() {
        const senderId = $('#customer').val();
        const recipientSelect = $('#recipient_customer');

        // Clear current options
        recipientSelect.empty().append('<option value="">Select recipient...</option>');

        // Reload customers excluding the sender
        this.loadCustomersForRecipient(senderId);
    }

    loadCustomersForRecipient(excludeId = null) {
        // Use the enhanced TransactionUtils customer loading with search functionality
        if (window.transactionUtils) {
            window.transactionUtils.loadCustomers('#recipient_customer', excludeId)
                .then(() => {
                    // Customer loading successful
                    console.log('Recipient customers loaded successfully');
                })
                .catch((error) => {
                    console.error('Error loading recipient customers:', error);
                    if (window.transactionUtils.showAlert) {
                        window.transactionUtils.showAlert('danger', 'Error loading recipient customers');
                    }
                });
        } else {
            console.error('TransactionUtils not available');
        }
    }

    loadRecipientDetails() {
        const recipientId = $('#recipient_customer').val();
        if (!recipientId) {
            $('#recipient-details').html('<div class="text-muted text-center py-3"><i class="bi bi-person"></i> Select recipient to view details</div>');
            return;
        }

        $.ajax({
            url: `/api/v1/customers/customers/${recipientId}/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                this.recipientDetails = data;
                let detailsHtml = `
                    <div class="list-group list-group-flush">
                        <div class="list-group-item"><strong>Name:</strong> ${data.name}</div>
                        <div class="list-group-item"><strong>Code:</strong> ${data.customer_code}</div>
                        <div class="list-group-item"><strong>Phone:</strong> ${data.phone || 'N/A'}</div>
                        <div class="list-group-item"><strong>Email:</strong> ${data.email || 'N/A'}</div>
                    </div>
                `;
                $('#recipient-details').html(detailsHtml);
            },
            error: () => {
                $('#recipient-details').html('<div class="text-danger text-center py-3"><i class="bi bi-exclamation-triangle"></i> Error loading recipient details</div>');
            }
        });
    }

    loadRecipientBalance() {
        const recipientId = $('#recipient_customer').val();
        if (!recipientId) {
            $('#recipient-balance').html('<div class="text-muted text-center py-3"><i class="bi bi-wallet"></i> Select recipient to view balance</div>');
            return;
        }

        $.ajax({
            url: `/api/v1/customers/customers/${recipientId}/balance/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                this.recipientBalances = data || [];
                let balanceHtml = '<div class="list-group list-group-flush">';

                if (this.recipientBalances.length > 0) {
                    this.recipientBalances.forEach(balance => {
                        const balanceClass = balance.balance >= 0 ? 'text-success' : 'text-danger';
                        balanceHtml += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>${balance.currency_code}</span>
                                <span class="${balanceClass}">${balance.formatted_balance}</span>
                            </div>
                        `;
                    });
                } else {
                    balanceHtml += '<div class="list-group-item text-muted text-center">No balance records found</div>';
                }

                balanceHtml += '</div>';
                $('#recipient-balance').html(balanceHtml);
            },
            error: () => {
                $('#recipient-balance').html('<div class="text-danger text-center py-3"><i class="bi bi-exclamation-triangle"></i> Error loading balance</div>');
            }
        });
    }

    checkSufficientBalance() {
        const transferAmount = parseFloat($('#from_amount').val()) || 0;
        const selectedCurrencyId = $('#from_currency').val();

        if (!selectedCurrencyId || transferAmount <= 0) {
            $('#balance-warning').empty();
            return;
        }

        // Get currency code from the selected option for balance lookup
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        console.log('Checking balance for currency:', selectedCurrencyCode);

        // Use global TransactionUtils.customerBalances instead of local copy
        const customerBalances = TransactionUtils.customerBalances || this.customerBalances || [];
        console.log('Available customer balances:', customerBalances);

        const balance = customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        const availableBalance = balance ? parseFloat(balance.balance) : 0;

        console.log('Found balance record:', balance);
        console.log('Available balance:', availableBalance);

        // Show warning if insufficient balance
        let warningHtml = '';
        if (transferAmount > availableBalance) {
            if (!customerBalances || customerBalances.length === 0) {
                warningHtml = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Info:</strong> Customer has no balance records. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else if (!balance) {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Customer has no ${selectedCurrencyCode} balance. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Transfer amount (${transferAmount}) exceeds available balance (${availableBalance}) for ${selectedCurrencyCode}.
                    </div>
                `;
            }
        }

        $('#balance-warning').html(warningHtml);
    }

    updateTransferSummary() {
        const transferAmount = parseFloat($('#from_amount').val()) || 0;
        const commissionAmount = parseFloat($('#commission_amount').val()) || 0;
        const currency = $('#from_currency option:selected').text();
        
        if (transferAmount <= 0) {
            $('#transfer-summary').hide();
            return;
        }

        const totalDeduction = transferAmount + commissionAmount;
        const recipientReceives = transferAmount;

        let summaryHtml = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Sender pays:</strong> ${totalDeduction} ${currency.split(' - ')[0] || ''}
                    <br><small class="text-muted">Transfer: ${transferAmount} + Fee: ${commissionAmount}</small>
                </div>
                <div class="col-md-6">
                    <strong>Recipient receives:</strong> ${recipientReceives} ${currency.split(' - ')[0] || ''}
                    <br><small class="text-muted">Full transfer amount</small>
                </div>
            </div>
        `;

        $('#transfer-summary-content').html(summaryHtml);
        $('#transfer-summary').show();
    }

    validateForm() {
        let isValid = true;
        const errors = [];

        // Validate sender and recipient are different
        const senderId = $('#customer').val();
        const recipientId = $('#recipient_customer').val();
        
        if (senderId && recipientId && senderId === recipientId) {
            errors.push('Sender and recipient must be different customers');
            isValid = false;
        }

        // Validate amount
        const transferAmount = parseFloat($('#from_amount').val());
        if (transferAmount <= 0) {
            errors.push('Transfer amount must be greater than zero');
            isValid = false;
        }

        // Check sufficient balance
        const selectedCurrencyId = $('#from_currency').val();
        if (this.customerBalances && selectedCurrencyId) {
            // Get currency code from the selected option for balance lookup
            const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
            const balance = this.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
            const availableBalance = balance ? parseFloat(balance.balance) : 0;

            if (transferAmount > availableBalance) {
                errors.push('Insufficient balance for this transfer');
                isValid = false;
            }
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    prepareFormData(action) {
        const formData = new FormData(this.form);
        const data = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // Set transfer type
        data.transfer_type = 'internal';

        // Set to_currency same as from_currency for internal transfers
        data.to_currency = data.from_currency;
        data.to_amount = data.from_amount;
        data.exchange_rate = 1.0;

        return data;
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            recipient: $('#recipient_customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            amount: formData.get('from_amount'),
            commission: formData.get('commission_amount'),
            reference: formData.get('internal_reference')
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>From:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.recipient && preview.recipient !== 'Select recipient...') {
            previewHtml += `<div class="list-group-item"><strong>To:</strong> ${preview.recipient}</div>`;
        }
        
        if (preview.amount && preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Amount:</strong> ${preview.amount} ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.reference) {
            previewHtml += `<div class="list-group-item"><strong>Reference:</strong> ${preview.reference}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    // Common methods
    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                // Set the transaction type for internal transfer
                const internalTransferType = data.results.find(type => type.code === 'INTERNAL_TRANSFER');
                if (internalTransferType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: internalTransferType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                TransactionUtils.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    setupValidation() {
        // Form validation setup
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter.dataset.action || 'save';
            if (this.validateForm()) {
                this.submitTransaction(action);
            }
        });
    }

    setCurrentUser() {
        // Set current user as the authorized user
        const currentUser = ArenaDoviz.auth.getCurrentUser();
        if (currentUser) {
            $('#authorized_by').val(currentUser.display_name || `${currentUser.first_name} ${currentUser.last_name}`);
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    submitTransaction(action) {
        if (!this.validateForm()) {
            return;
        }

        TransactionUtils.submitTransaction(this.form, action)
            .then((response) => {
                const transactionId = response.id;
                const files = $('#document_files')[0].files;

                if (files.length > 0) {
                    TransactionUtils.uploadDocuments(transactionId, files)
                        .then(() => {
                            TransactionUtils.showAlert('success', 'Internal transfer and documents uploaded successfully');
                            setTimeout(() => {
                                window.location.href = `/transactions/${transactionId}/`;
                            }, 2000);
                        })
                        .catch((error) => {
                            TransactionUtils.showAlert('warning', `Internal transfer created but document upload failed: ${error.message}`);
                            setTimeout(() => {
                                window.location.href = `/transactions/${transactionId}/`;
                            }, 5000);
                        });
                } else {
                    TransactionUtils.showAlert('success', 'Internal transfer created successfully');
                    setTimeout(() => {
                        window.location.href = `/transactions/${transactionId}/`;
                    }, 2000);
                }
            })
            .catch((error) => {
                TransactionUtils.showAlert('danger', `Error creating internal transfer: ${error.message}`);
            });
    }
}

// Initialize when DOM is ready
$(document).ready(() => {
    new InternalTransferForm();
});
