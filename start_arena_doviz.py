#!/usr/bin/env python
"""
Arena Doviz Exchange Accounting System - Startup Script

This script provides a simple way to start the Arena Doviz application
with proper configuration and error handling.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def main():
    """Main startup function."""
    print("=" * 60)
    print("Arena Doviz Exchange Accounting System")
    print("=" * 60)
    
    # Change to src directory
    src_dir = Path(__file__).parent / 'src'
    if not src_dir.exists():
        print("❌ Error: src directory not found!")
        sys.exit(1)
    
    os.chdir(src_dir)
    print(f"📁 Working directory: {src_dir.absolute()}")
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. Consider activating venv.")
    
    # Run system checks
    print("\n🔍 Running system checks...")
    try:
        result = subprocess.run([sys.executable, 'manage.py', 'check'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print("❌ System check failed:")
            print(result.stderr)
            sys.exit(1)
        print("✅ System check passed")
    except subprocess.TimeoutExpired:
        print("⚠️  System check timed out, continuing anyway...")
    except Exception as e:
        print(f"⚠️  System check error: {e}")
    
    # Collect static files
    print("\n📦 Collecting static files...")
    try:
        subprocess.run([sys.executable, 'manage.py', 'collectstatic', '--noinput'], 
                      check=True, capture_output=True)
        print("✅ Static files collected")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Static files collection failed: {e}")
    
    # Start the development server
    print("\n🚀 Starting Arena Doviz development server...")
    print("📍 Server will be available at: http://0.0.0.0:8000")
    print("📍 Local access: http://127.0.0.1:8000")
    print("📍 Network access: http://[your-ip]:8000")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 60)

    try:
        subprocess.run([sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'])
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
