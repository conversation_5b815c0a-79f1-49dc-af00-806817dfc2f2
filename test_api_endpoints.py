#!/usr/bin/env python
"""
Test Arena Doviz API endpoints directly to identify authentication/API issues.
"""

import requests
import json
import sys
from urllib.parse import urljoin

# Configuration
BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api/v1"

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title):
    print(f"\n--- {title} ---")

def test_login():
    """Test user authentication and get token."""
    print_header("AUTHENTICATION TEST")

    login_url = f"{API_BASE}/accounts/jwt/token/"
    login_data = {
        "username": "admin",
        "password": "admin123"  # Default admin password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"Login URL: {login_url}")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text[:500]}")
        
        if response.status_code == 200:
            data = response.json()
            if 'access' in data:
                print("✅ Authentication successful")
                return data['access']
            else:
                print("❌ No access token in response")
                return None
        else:
            print("❌ Authentication failed")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_customers_api(token):
    """Test customers API endpoint."""
    print_header("CUSTOMERS API TEST")
    
    customers_url = f"{API_BASE}/customers/customers/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    } if token else {}
    
    try:
        response = requests.get(customers_url, headers=headers)
        print(f"Customers URL: {customers_url}")
        print(f"Status Code: {response.status_code}")
        print(f"Headers sent: {headers}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Customers API working - Found {len(data.get('results', []))} customers")
            
            # Show first few customers
            if data.get('results'):
                print("\nFirst 3 customers:")
                for customer in data['results'][:3]:
                    print(f"  ID: {customer.get('id')}, Code: {customer.get('customer_code')}, Name: {customer.get('first_name')} {customer.get('last_name')}")
            return True
        else:
            print(f"❌ Customers API failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Customers API error: {e}")
        return False

def test_transactions_api(token):
    """Test transactions API endpoint."""
    print_header("TRANSACTIONS API TEST")
    
    transactions_url = f"{API_BASE}/transactions/transactions/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    } if token else {}
    
    try:
        response = requests.get(transactions_url, headers=headers)
        print(f"Transactions URL: {transactions_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Transactions API working - Found {len(data.get('results', []))} transactions")
            return True
        else:
            print(f"❌ Transactions API failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Transactions API error: {e}")
        return False

def test_transfer_transactions_api(token):
    """Test transfer transactions filtering."""
    print_header("TRANSFER TRANSACTIONS API TEST")
    
    # Test the specific filtering that's failing
    transfer_url = f"{API_BASE}/transactions/transactions/?transaction_type_code=TRANSFER"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    } if token else {}
    
    try:
        response = requests.get(transfer_url, headers=headers)
        print(f"Transfer URL: {transfer_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Transfer filtering working - Found {len(data.get('results', []))} transfer transactions")
            
            # Also test other types
            for tx_type in ['EXCHANGE', 'DEPOSIT', 'WITHDRAWAL']:
                type_url = f"{API_BASE}/transactions/transactions/?transaction_type_code={tx_type}"
                type_response = requests.get(type_url, headers=headers)
                if type_response.status_code == 200:
                    type_data = type_response.json()
                    print(f"  {tx_type}: {len(type_data.get('results', []))} transactions")
                else:
                    print(f"  {tx_type}: API failed ({type_response.status_code})")
            
            return True
        else:
            print(f"❌ Transfer filtering failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Transfer filtering error: {e}")
        return False

def test_server_running():
    """Test if server is running."""
    print_header("SERVER CONNECTIVITY TEST")
    
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"Server URL: {BASE_URL}")
        print(f"Status Code: {response.status_code}")
        print("✅ Server is running")
        return True
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False

def main():
    """Run comprehensive API tests."""
    print_header("ARENA DOVIZ API DIAGNOSTIC")
    
    # Test server connectivity
    if not test_server_running():
        print("\n❌ CRITICAL: Server is not running!")
        print("Please start the server with: python start_arena_doviz.py")
        return
    
    # Test authentication
    token = test_login()
    
    # Test API endpoints
    results = {
        'customers': test_customers_api(token),
        'transactions': test_transactions_api(token),
        'transfer_filtering': test_transfer_transactions_api(token)
    }
    
    print_header("API TEST SUMMARY")
    
    failed_tests = []
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.upper()}: {status}")
        if not result:
            failed_tests.append(test)
    
    if failed_tests:
        print(f"\n❌ FAILED TESTS: {', '.join(failed_tests)}")
        
        if not token:
            print("\nRECOMMENDED ACTIONS:")
            print("1. Check if admin user exists with correct password")
            print("2. Verify JWT authentication is properly configured")
            print("3. Check Django settings for authentication backends")
        
        if 'customers' in failed_tests:
            print("4. Check customer API permissions and viewset configuration")
        
        if 'transfer_filtering' in failed_tests:
            print("5. Check transaction filtering logic in views.py")
    else:
        print("\n✅ ALL API TESTS PASSED")
        print("Issue is likely in frontend JavaScript or template rendering")

if __name__ == '__main__':
    main()
