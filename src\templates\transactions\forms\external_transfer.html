{% extends 'transactions/base_form.html' %}
{% load i18n static %}

{% block transaction_specific_fields %}
<!-- External Transfer Details -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-bank"></i>
            {% trans "External Transfer Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Transfer Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.01" required placeholder="0.00">
                    <div class="form-text">{% trans "Amount to transfer to external bank" %}</div>
                </div>
            </div>
        </div>

        <!-- Hidden fields for API compatibility - external transfers use same currency and amount -->
        <input type="hidden" id="to_currency" name="to_currency">
        <input type="hidden" id="to_amount" name="to_amount">
        <input type="hidden" id="exchange_rate" name="exchange_rate" value="1.0">

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Transfer Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="autoCalculateExternalFee()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" placeholder="0.00">
                    <div class="form-text" id="commission_info">{% trans "Bank transfer fee" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="external_reference" class="form-label">{% trans "External Reference" %}</label>
                    <input type="text" class="form-control" id="external_reference" name="external_reference" placeholder="{% trans 'Bank reference number...' %}">
                    <div class="form-text">{% trans "Bank-provided reference number" %}</div>
                </div>
            </div>
        </div>
        
        <!-- Balance Warning -->
        <div id="balance-warning"></div>
    </div>
</div>

<!-- Bank Details -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-building"></i>
            {% trans "Bank Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="bank_name" class="form-label">{% trans "Bank Name" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="bank_name" name="bank_name" required placeholder="{% trans 'Bank name...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="bank_code" class="form-label">{% trans "Bank Code" %}</label>
                    <input type="text" class="form-control" id="bank_code" name="bank_code" placeholder="{% trans 'Bank code/SWIFT...' %}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="account_number" class="form-label">{% trans "Account Number" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="account_number" name="account_number" required placeholder="{% trans 'Account number...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="account_name" class="form-label">{% trans "Account Holder Name" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="account_name" name="account_name" required placeholder="{% trans 'Account holder name...' %}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="routing_number" class="form-label">{% trans "Routing Number" %}</label>
                    <input type="text" class="form-control" id="routing_number" name="routing_number" placeholder="{% trans 'Routing/sort code...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="bank_address" class="form-label">{% trans "Bank Address" %}</label>
                    <input type="text" class="form-control" id="bank_address" name="bank_address" placeholder="{% trans 'Bank address...' %}">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Instructions -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-file-text"></i>
            {% trans "Transfer Instructions" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="transfer_purpose" class="form-label">{% trans "Purpose of Transfer" %}</label>
            <select class="form-select" id="transfer_purpose" name="transfer_purpose">
                <option value="">{% trans "Select purpose..." %}</option>
                <option value="business">{% trans "Business Transaction" %}</option>
                <option value="personal">{% trans "Personal Transfer" %}</option>
                <option value="investment">{% trans "Investment" %}</option>
                <option value="loan">{% trans "Loan Payment" %}</option>
                <option value="other">{% trans "Other" %}</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="transfer_instructions" class="form-label">{% trans "Special Instructions" %}</label>
            <textarea class="form-control" id="transfer_instructions" name="transfer_instructions" rows="3" placeholder="{% trans 'Special instructions for the bank...' %}"></textarea>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="{% static 'js/transactions/external_transfer.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form is already initialized by external_transfer.js

    // Auto-populate to_currency and to_amount for external transfers
    $('#from_currency').on('change', function() {
        $('#to_currency').val($(this).val());
        console.log('✅ to_currency auto-populated:', $(this).val());
    });

    $('#from_amount').on('input', function() {
        $('#to_amount').val($(this).val());
        console.log('✅ to_amount auto-populated:', $(this).val());
    });
});

// Unified Service Fee Function for External Transfer
function autoCalculateExternalFee() {
    const amount = $('#from_amount').val();
    const currency = $('#from_currency').val();

    if (!amount || amount <= 0) {
        showAlert('warning', '{% trans "Please enter a valid transfer amount first" %}');
        return;
    }

    if (!currency) {
        showAlert('warning', '{% trans "Please select a currency first" %}');
        return;
    }

    // Use the unified system
    window.transactionUtils.autoCalculateServiceFee('EXTERNAL_TRANSFER', amount, currency, {
        feeField: '#commission_amount',
        infoField: '#commission_info'
    });
}

// Auto-trigger fee calculation when amount changes
$(document).ready(function() {
    $('#from_amount').on('input', function() {
        const currency = $('#from_currency').val();
        if (currency && $(this).val() > 0) {
            // Debounce the auto-calculation
            clearTimeout(window.externalFeeTimeout);
            window.externalFeeTimeout = setTimeout(() => {
                autoCalculateExternalFee();
            }, 1500);
        }
    });

    $('#from_currency').on('change', function() {
        const amount = $('#from_amount').val();
        if (amount && amount > 0) {
            autoCalculateExternalFee();
        }
    });
});
</script>
{% endblock %}
