# Generated by Django 4.2.23 on 2025-08-21 21:06

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0002_customer_preferred_couriers'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customer',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Primary contact phone number (optional)', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone number'),
        ),
    ]
