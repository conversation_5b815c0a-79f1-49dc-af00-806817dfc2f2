{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Edit Customer" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-person-gear"></i>
                {% trans "Edit Customer" %}
            </h1>
            <div>
                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back" %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pencil"></i>
                    {% trans "Customer Information" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="edit-customer-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_type" class="form-label">{% trans "Customer Type" %}</label>
                                <select class="form-select" id="customer_type" name="customer_type" required>
                                    <option value="">{% trans "Select customer type" %}</option>
                                    <option value="individual">{% trans "Individual" %}</option>
                                    <option value="corporate">{% trans "Corporate" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">{% trans "Status" %}</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active">{% trans "Active" %}</option>
                                    <option value="inactive">{% trans "Inactive" %}</option>
                                    <option value="suspended">{% trans "Suspended" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Individual Customer Fields -->
                    <div id="individual-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">{% trans "First Name" %}</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">{% trans "Last Name" %}</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Corporate Customer Fields -->
                    <div id="corporate-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">{% trans "Company Name" %}</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tax_number" class="form-label">{% trans "Tax Number" %}</label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Common Fields -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone_number" class="form-label">{% trans "Phone Number" %}</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">{% trans "Email" %}</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>

                    <!-- WhatsApp Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="whatsapp_group_id" class="form-label">
                                    <i class="bi bi-whatsapp me-1"></i>{% trans "WhatsApp Group ID" %}
                                </label>
                                <input type="text" class="form-control" id="whatsapp_group_id" name="whatsapp_group_id"
                                       placeholder="{% trans 'Enter WhatsApp group ID for customer communications' %}">
                                <div class="form-text">{% trans "Optional: WhatsApp group ID for sending transaction notifications" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="preferred_location" class="form-label">{% trans "Preferred Location" %}</label>
                                <select class="form-select" id="preferred_location" name="preferred_location">
                                    <option value="">{% trans "Select location" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="risk_level" class="form-label">{% trans "Risk Level" %}</label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="low">{% trans "Low" %}</option>
                                    <option value="medium">{% trans "Medium" %}</option>
                                    <option value="high">{% trans "High" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">{% trans "Address" %}</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            {% trans "Cancel" %}
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            {% trans "Save Changes" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let customerId = null;

// Authentication utility functions
function getAuthToken() {
    return localStorage.getItem('arena_access_token') || '';
}

function getAuthHeaders() {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

$(document).ready(function() {
    // Get customer ID from URL
    const pathParts = window.location.pathname.split('/');
    customerId = pathParts[pathParts.length - 3]; // Get UUID from URL
    
    if (customerId) {
        loadCustomerData();
        loadLocations();
    }

    // Handle customer type change
    $('#customer_type').on('change', function() {
        const customerType = $(this).val();
        if (customerType === 'individual') {
            $('#individual-fields').show();
            $('#corporate-fields').hide();
        } else if (customerType === 'corporate') {
            $('#individual-fields').hide();
            $('#corporate-fields').show();
        } else {
            $('#individual-fields').hide();
            $('#corporate-fields').hide();
        }
    });

    // Handle form submission
    $('#edit-customer-form').on('submit', function(e) {
        e.preventDefault();
        saveCustomer();
    });
});

function loadCustomerData() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            populateForm(data);
        },
        error: function(xhr) {
            console.error('Error loading customer data:', xhr);
            showAlert('danger', '{% trans "Failed to load customer data" %}');
        }
    });
}

function populateForm(customer) {
    $('#customer_type').val(customer.customer_type).trigger('change');
    $('#status').val(customer.status);
    $('#first_name').val(customer.first_name || '');
    $('#last_name').val(customer.last_name || '');
    $('#company_name').val(customer.company_name || '');
    $('#tax_number').val(customer.tax_number || '');
    $('#phone_number').val(customer.phone_number || '');
    $('#email').val(customer.email || '');
    $('#whatsapp_group_id').val(customer.whatsapp_group_id || '');
    $('#preferred_location').val(customer.preferred_location || '');
    $('#risk_level').val(customer.risk_level || 'low');
    $('#address').val(customer.address || '');
    $('#notes').val(customer.notes || '');
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#preferred_location');
            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        },
        error: function(xhr) {
            console.error('Error loading locations:', xhr);
        }
    });
}

function saveCustomer() {
    const formData = new FormData(document.getElementById('edit-customer-form'));
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    console.log('🔄 Saving customer data:', data);

    const headers = getAuthHeaders();
    headers['Content-Type'] = 'application/json';

    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/`,
        method: 'PUT',
        headers: headers,
        data: JSON.stringify(data),
        success: function(response) {
            console.log('✅ Customer updated successfully:', response);
            showAlert('success', '{% trans "Customer updated successfully" %}');
            setTimeout(() => {
                window.location.href = `/customers/${customerId}/`;
            }, 1500);
        },
        error: function(xhr) {
            console.error('❌ Error saving customer:', xhr);
            console.error('Status:', xhr.status);
            console.error('Response Text:', xhr.responseText);
            console.error('Response JSON:', xhr.responseJSON);
            console.error('Request Data:', data);

            let errorMessage = '{% trans "Failed to update customer" %}';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join(', ');
                } else if (xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
            }

            showAlert('danger', errorMessage);
        }
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert at the top of the content
    $('.card-body').first().prepend(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
