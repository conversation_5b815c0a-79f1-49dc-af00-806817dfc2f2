{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block transaction_specific_fields %}
<!-- Currency Exchange Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-arrow-left-right"></i>
            {% trans "Currency Exchange Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "From Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.01" required placeholder="0.00">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_currency" class="form-label">{% trans "To Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="to_currency" name="to_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_amount" class="form-label">{% trans "Receive Amount" %}</label>
                    <input type="number" class="form-control" id="to_amount" name="to_amount" step="0.01" placeholder="0.00" readonly>
                    <div class="form-text">{% trans "Calculated automatically based on current rate" %}</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="exchange_rate" class="form-label">{% trans "Exchange Rate" %}</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" placeholder="0.000000" readonly>
                        <button class="btn btn-outline-primary" type="button" id="get-current-rate">
                            <i class="bi bi-arrow-clockwise"></i> {% trans "Get Rate" %}
                        </button>
                    </div>
                    <div class="form-text">{% trans "Current market rate will be applied automatically" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Service Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="autoCalculateExchangeFee()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" placeholder="0.00">
                    <div class="form-text" id="commission_info">{% trans "Optional service fee" %}</div>
                </div>
            </div>
        </div>

        <!-- Exchange Rate Information -->
        <div class="alert alert-info" id="rate-info" style="display: none;">
            <i class="bi bi-info-circle"></i>
            <span id="rate-info-text"></span>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/exchange.js"></script>
<script>
// Unified Exchange Rate & Service Fee Functions for Exchange Form
function autoFetchExchangeRate() {
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const fromAmount = $('#from_amount').val();

    if (!fromCurrency || !toCurrency) {
        showAlert('warning', '{% trans "Please select both currencies first" %}');
        return;
    }

    if (!fromAmount || fromAmount <= 0) {
        showAlert('warning', '{% trans "Please enter a valid amount first" %}');
        return;
    }

    // Use the unified system
    window.transactionUtils.autoCalculateExchangeRate(fromCurrency, toCurrency, fromAmount, {
        exchangeRateField: '#exchange_rate',
        toAmountField: '#to_amount',
        callback: (rate, toAmount) => {
            $('#rate-info-text').text(`{% trans "Rate updated:" %} 1 ${$('#from_currency option:selected').text()} = ${rate} ${$('#to_currency option:selected').text()}`);
            $('#rate-info').show();
        }
    });
}

function autoCalculateExchangeFee() {
    const fromAmount = $('#from_amount').val();
    const fromCurrency = $('#from_currency').val();

    if (!fromAmount || fromAmount <= 0) {
        showAlert('warning', '{% trans "Please enter a valid amount first" %}');
        return;
    }

    if (!fromCurrency) {
        showAlert('warning', '{% trans "Please select a currency first" %}');
        return;
    }

    // Use the unified system
    window.transactionUtils.autoCalculateServiceFee('EXCHANGE', fromAmount, fromCurrency, {
        feeField: '#commission_amount',
        infoField: '#commission_info'
    });
}

// Auto-trigger exchange rate calculation when currencies or amount change
$(document).ready(function() {
    $('#from_currency, #to_currency').on('change', function() {
        const fromAmount = $('#from_amount').val();
        if (fromAmount && fromAmount > 0) {
            autoFetchExchangeRate();
        }
    });

    $('#from_amount').on('input', function() {
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();
        if (fromCurrency && toCurrency && $(this).val() > 0) {
            // Debounce the auto-calculation
            clearTimeout(window.exchangeRateTimeout);
            window.exchangeRateTimeout = setTimeout(() => {
                autoFetchExchangeRate();
            }, 1000);
        }
    });

    // Connect the existing "Get Rate" button to the unified system
    $('#get-current-rate').on('click', autoFetchExchangeRate);
});
</script>
{% endblock %}
