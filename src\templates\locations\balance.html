{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Location Balance" %}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/datatables.min.css' %}" rel="stylesheet">
<style>
    .balance-card {
        background: white;
        border-radius: 0;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .balance-card .card-header {
        background-color: #000d28;
        color: white;
        border-bottom: 1px solid #dee2e6;
    }
    .currency-balance {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0;
        transition: transform 0.2s;
        margin-bottom: 1rem;
    }
    .currency-balance:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .balance-amount {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .balance-positive {
        color: #198754;
    }
    .balance-negative {
        color: #dc3545;
    }
    .balance-zero {
        color: #6c757d;
    }
    .currency-code {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: normal;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">{% trans "Location Balance" %}</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'locations_web:list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        {% trans "Back to List" %}
                    </a>
                    <button class="btn btn-primary" onclick="refreshBalances()">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card balance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt me-2"></i>
                        <span id="location-name">{% trans "Loading..." %}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>{% trans "Code" %}:</strong> <span id="location-code">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Type" %}:</strong> <span id="location-type">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Status" %}:</strong> <span id="location-status">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>{% trans "Last Updated" %}:</strong> <span id="last-updated">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Currency Balances -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card balance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-wallet2 me-2"></i>
                        {% trans "Currency Balances" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div id="currency-balances" class="row">
                        <div class="col-12 text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">{% trans "Loading..." %}</span>
                            </div>
                            <p class="mt-2">{% trans "Loading balances..." %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance History -->
    <div class="row">
        <div class="col-12">
            <div class="card balance-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        {% trans "Balance History" %}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="currency-filter" class="form-label">{% trans "Currency" %}</label>
                            <select class="form-select" id="currency-filter">
                                <option value="">{% trans "All Currencies" %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date-from" class="form-label">{% trans "From Date" %}</label>
                            <input type="date" class="form-control" id="date-from">
                        </div>
                        <div class="col-md-3">
                            <label for="date-to" class="form-label">{% trans "To Date" %}</label>
                            <input type="date" class="form-control" id="date-to">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-outline-primary" onclick="applyFilters()">
                                    <i class="bi bi-funnel"></i>
                                    {% trans "Apply Filters" %}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="balance-history-table" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Transaction" %}</th>
                                    <th>{% trans "Customer" %}</th>
                                    <th>{% trans "Currency" %}</th>
                                    <th>{% trans "Debit" %}</th>
                                    <th>{% trans "Credit" %}</th>
                                    <th>{% trans "Balance" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- DataTables will populate this -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/datatables.min.js' %}"></script>
<script>
let locationId = '';
let balanceHistoryTable;

document.addEventListener('DOMContentLoaded', function() {
    // Get location ID from URL
    const pathParts = window.location.pathname.split('/');
    locationId = pathParts[pathParts.length - 3]; // Get ID from URL pattern /locations/{id}/balance/

    console.log('Loading balance for location ID:', locationId);

    loadLocationInfo();
    loadCurrencyBalances();
    initializeBalanceHistoryTable();
    loadCurrencyOptions();
});

function loadLocationInfo() {
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#location-name').text(data.name || '-');
            $('#location-code').text(data.code || '-');
            $('#location-type').text(data.get_location_type_display || data.location_type || '-');
            $('#location-status').html(data.is_active ? 
                '<span class="badge bg-success">{% trans "Active" %}</span>' : 
                '<span class="badge bg-danger">{% trans "Inactive" %}</span>');
            $('#last-updated').text(new Date().toLocaleString());
        },
        error: function(xhr) {
            console.error('Error loading location info:', xhr);
            showAlert('danger', '{% trans "Failed to load location information" %}');
        }
    });
}

function loadCurrencyBalances() {
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/balances/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayCurrencyBalances(data);
        },
        error: function(xhr) {
            console.error('Error loading currency balances:', xhr);
            $('#currency-balances').html(`
                <div class="col-12 text-center">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        {% trans "Failed to load currency balances" %}
                    </div>
                </div>
            `);
        }
    });
}

function displayCurrencyBalances(balances) {
    if (!balances || balances.length === 0) {
        $('#currency-balances').html(`
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    {% trans "No balance data available" %}
                </div>
            </div>
        `);
        return;
    }

    let html = '';
    balances.forEach(balance => {
        const amount = parseFloat(balance.balance || 0);
        let balanceClass = 'balance-zero';
        if (amount > 0) balanceClass = 'balance-positive';
        else if (amount < 0) balanceClass = 'balance-negative';

        html += `
            <div class="col-md-4 col-lg-3">
                <div class="card currency-balance">
                    <div class="card-body text-center">
                        <h6 class="card-title">
                            ${balance.currency_name || balance.currency_code}
                            <span class="currency-code">(${balance.currency_code})</span>
                        </h6>
                        <div class="balance-amount ${balanceClass}">
                            ${balance.formatted_balance || balance.balance || '0.00'}
                        </div>
                        <small class="text-muted">
                            {% trans "Last updated" %}: ${new Date(balance.last_updated || Date.now()).toLocaleDateString()}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });

    $('#currency-balances').html(html);
}

function loadCurrencyOptions() {
    $.ajax({
        url: '/api/v1/currencies/currencies/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#currency-filter');
            data.results.forEach(currency => {
                select.append(`<option value="${currency.id}">${currency.name} (${currency.code})</option>`);
            });
        },
        error: function(xhr) {
            console.error('Error loading currencies:', xhr);
        }
    });
}

function initializeBalanceHistoryTable() {
    balanceHistoryTable = $('#balance-history-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/api/v1/transactions/balance-entries/',
            type: 'GET',
            headers: getAuthHeaders(),
            data: function(d) {
                const filters = {
                    page: Math.floor(d.start / d.length) + 1,
                    page_size: d.length,
                    search: d.search.value,
                    location: locationId,
                    ordering: d.order.length > 0 ?
                        (d.order[0].dir === 'desc' ? '-' : '') + 
                        ['created_at', 'transaction__transaction_number', 'customer__first_name', 'currency__code', 'amount', 'amount', 'running_balance'][d.order[0].column] :
                        '-created_at'
                };

                // Add date filters
                const dateFrom = $('#date-from').val();
                const dateTo = $('#date-to').val();
                const currencyFilter = $('#currency-filter').val();

                if (dateFrom) filters.date_from = dateFrom;
                if (dateTo) filters.date_to = dateTo;
                if (currencyFilter) filters.currency = currencyFilter;

                return filters;
            },
            error: function(xhr, error, thrown) {
                console.error('Balance history DataTables AJAX error:', error, thrown);
                showAlert('danger', '{% trans "Failed to load balance history" %}');
            }
        },
        columns: [
            { data: 'created_at', name: 'created_at', render: function(data) {
                return new Date(data).toLocaleString();
            }},
            { data: 'transaction.transaction_number', name: 'transaction__transaction_number', defaultContent: '-' },
            { data: 'customer_name', name: 'customer__first_name', defaultContent: '-' },
            { data: 'currency_code', name: 'currency__code' },
            { data: 'amount', name: 'amount', render: function(data, type, row) {
                return parseFloat(data) < 0 ? row.formatted_amount || data : '-';
            }},
            { data: 'amount', name: 'amount', render: function(data, type, row) {
                return parseFloat(data) > 0 ? row.formatted_amount || data : '-';
            }},
            { data: 'formatted_running_balance', name: 'running_balance', defaultContent: '-' },
            { data: null, orderable: false }
        ],
        order: [[0, 'desc']], // Order by date
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        responsive: true,
        columnDefs: [
            {
                targets: -1, // Actions column
                render: function(data, type, row) {
                    let actions = '';
                    if (row.transaction && row.transaction.id) {
                        actions += `
                            <a href="/transactions/detail/${row.transaction.id}/" class="btn btn-outline-primary btn-sm" title="{% trans 'View Transaction' %}">
                                <i class="bi bi-eye"></i>
                            </a>
                        `;
                    }
                    return actions || '-';
                }
            }
        ],
        language: {
            search: '{% trans "Search:" %}',
            lengthMenu: '{% trans "Show _MENU_ entries" %}',
            info: '{% trans "Showing _START_ to _END_ of _TOTAL_ entries" %}',
            infoEmpty: '{% trans "Showing 0 to 0 of 0 entries" %}',
            infoFiltered: '{% trans "(filtered from _MAX_ total entries)" %}',
            loadingRecords: '{% trans "Loading..." %}',
            processing: '{% trans "Processing..." %}',
            zeroRecords: '{% trans "No balance history found" %}',
            paginate: {
                first: '{% trans "First" %}',
                last: '{% trans "Last" %}',
                next: '{% trans "Next" %}',
                previous: '{% trans "Previous" %}'
            }
        }
    });
}

function refreshBalances() {
    loadCurrencyBalances();
    if (balanceHistoryTable) {
        balanceHistoryTable.ajax.reload();
    }
    showAlert('success', '{% trans "Balances refreshed successfully" %}');
}

function applyFilters() {
    if (balanceHistoryTable) {
        balanceHistoryTable.ajax.reload();
    }
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}
</script>
{% endblock %}
