# Arena Doviz Exchange Accounting System

A comprehensive exchange office management system built with Django, designed for multi-location operations with support for USD, AED, and IRR currencies.

## 🌟 Features

- **Multi-location Support**: Manage operations across Istanbul, Tabriz, Tehran, Dubai, and China
- **Multi-currency Operations**: Handle USD, AED, and IRR with location-specific exchange rates
- **Double-entry Bookkeeping**: Comprehensive financial tracking and balance management
- **User Management**: Role-based access control (Admin, Accountant, Branch Employee, Viewer, Courier)
- **Customer Management**: Support for both individual and corporate customers
- **Transaction Processing**: Multi-step transactions with delivery tracking
- **WhatsApp Integration**: Desktop WhatsApp integration for team communication
- **Comprehensive Reporting**: Financial reports, customer statements, and analytics
- **Audit Trail**: Complete logging of all system activities
- **REST API**: Full-featured API with Swagger documentation
- **Real-time Dashboard**: Live updates and analytics

## 🛠 Technology Stack

- **Backend**: Django 4.2+ with Django REST Framework
- **Database**: PostgreSQL 15+
- **Cache**: Redis
- **Frontend**: Bootstrap 5 with responsive design
- **API Documentation**: DRF Spectacular (Swagger/ReDoc)
- **Deployment**: Docker with Nginx reverse proxy
- **Authentication**: Token-based and session authentication

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd exchange-accounting
```

2. **Copy environment file:**
```bash
cp .env.example .env
```

3. **Start the application:**
```bash
cd deployment
docker-compose up --build
```

4. **Access the application:**
- **Web Interface**: http://localhost:8080
- **API Documentation**: http://localhost:8080/api/docs/
- **Admin Panel**: http://localhost:8080/admin/

### Default Credentials

- **Username**: `admin`
- **Password**: `admin123`

## 📁 Project Structure

```
exchange-accounting/
├── src/                    # Django application source
│   ├── apps/              # Django apps
│   │   ├── accounts/      # User management and authentication
│   │   ├── core/          # Core utilities and base models
│   │   ├── customers/     # Customer management
│   │   ├── locations/     # Multi-location support
│   │   ├── currencies/    # Currency and exchange rate management
│   │   ├── transactions/  # Transaction processing
│   │   └── reports/       # Reporting and analytics
│   ├── config/            # Django settings and configuration
│   ├── static/            # Static files (CSS, JS, images)
│   └── templates/         # HTML templates
├── deployment/            # Docker configuration
├── docs/                  # Documentation
└── tests/                 # Test files
```

## 🔌 API Documentation

The system provides a comprehensive REST API documented with Swagger/OpenAPI:

- **Swagger UI**: `/api/docs/`
- **ReDoc**: `/api/redoc/`
- **OpenAPI Schema**: `/api/schema/`

### Key API Endpoints

| Endpoint | Description |
|----------|-------------|
| `/api/v1/auth/` | Authentication and user management |
| `/api/v1/customers/` | Customer management |
| `/api/v1/locations/` | Location management |
| `/api/v1/currencies/` | Currency and exchange rate management |
| `/api/v1/transactions/` | Transaction processing |
| `/api/v1/reports/` | Reporting and analytics |

### Authentication

The API supports multiple authentication methods:

```bash
# Token Authentication
curl -H "Authorization: Token your-token-here" http://localhost:8080/api/v1/customers/

# Session Authentication (for web interface)
# Automatically handled by Django sessions
```

## 💻 Development

### Local Development Setup

1. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies:**
```bash
pip install -r deployment/requirements.txt
```

3. **Setup database:**
```bash
cd src
python manage.py migrate
python manage.py createsuperuser
```

4. **Run development server:**
```bash
# Option 1: Use the startup script (recommended)
python ../start_arena_doviz.py

# Option 2: Manual start
python manage.py runserver
```

### Running Tests

```bash
cd src
python manage.py test
```

## ⚙️ Configuration

### Environment Variables

Key environment variables (see `.env` file):

| Variable | Description | Default |
|----------|-------------|---------|
| `SECRET_KEY` | Django secret key | Required |
| `DEBUG` | Debug mode | `false` |
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_URL` | Redis connection string | Required |
| `ALLOWED_HOSTS` | Comma-separated allowed hosts | `localhost,127.0.0.1` |

### Multi-location Configuration

The system supports multiple locations with individual settings:

| Location | Code | Country | Role |
|----------|------|---------|------|
| **Tabriz** | TBZ | Iran | Main office |
| **Istanbul** | IST | Turkey | European operations |
| **Tehran** | THR | Iran | Secondary office |
| **Dubai** | DXB | UAE | UAE operations |
| **China** | CHN | China | Asian operations |

### Currency Configuration

| Currency | Code | Symbol | Decimal Places | Role |
|----------|------|--------|----------------|------|
| **US Dollar** | USD | $ | 2 | Base currency |
| **UAE Dirham** | AED | د.إ | 2 | Secondary |
| **Iranian Rial** | IRR | ﷼ | 0 | Local |

## 🔒 Security Features

- **Authentication**: Token-based and session authentication
- **Authorization**: Role-based access control with 5 user roles
- **Rate Limiting**: API rate limiting (100 requests/minute per IP)
- **Input Validation**: Comprehensive data validation and sanitization
- **Audit Logging**: Complete audit trail of all actions
- **IP Restrictions**: Location-based IP whitelisting
- **Session Management**: Secure session handling with Redis

## 💼 Business Logic

### Double-entry Bookkeeping

The system implements proper double-entry bookkeeping:
- Every transaction creates balanced debit and credit entries
- Customer balances are tracked per currency and location
- Company balances are maintained separately
- Running balances are calculated automatically

### User Roles and Permissions

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access, user management, system configuration |
| **Accountant** | Financial operations, reporting, transaction approval |
| **Branch Employee** | Daily operations, customer service, transaction processing |
| **Viewer** | Read-only access to reports and data |
| **Courier** | Delivery tracking and receipt management |

## 🚀 Deployment

### Docker Deployment

```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose logs -f web

# Scale services
docker-compose up --scale web=3
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:
- **Documentation**: `/docs/`
- **API Documentation**: `/api/docs/`
- **Issue Tracking**: GitHub Issues

## 📋 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and changes.

---

**Arena Doviz Exchange Accounting System** - Built with ❤️ for efficient exchange office management.
