/**
 * International Transfer Transaction Form Handler
 */

class InternationalTransferForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'INTERNATIONAL_TRANSFER';
        this.exchangeRates = {};
        this.currentBalance = 0;
        this.initialized = false; // Prevent multiple initializations

        // Only initialize if form exists and not already initialized
        if (this.form && !this.initialized) {
            this.init();
        } else if (this.initialized) {
            console.warn('InternationalTransferForm already initialized, skipping');
        } else {
            console.warn('Transaction form not found, skipping InternationalTransferForm initialization');
        }
    }

    init() {
        if (this.initialized) {
            console.log('InternationalTransferForm already initialized, skipping');
            return;
        }

        console.log('InternationalTransferForm.init() called');
        this.initialized = true; // Mark as initialized

        this.loadFormData();
        this.setupEventListeners();
        this.setupValidation();
        this.setCurrentUser();
    }

    loadFormData() {
        this.loadTransactionTypes();

        // Wait for global TransactionUtils instance to be available
        const waitForTransactionUtils = () => {
            if (window.transactionUtils) {
                window.transactionUtils.loadCustomers();
                window.transactionUtils.loadLocations();
                window.transactionUtils.loadCurrencies();

                // Pre-populate customer from URL parameter if present
                window.transactionUtils.prePopulateCustomerFromUrl();
            } else {
                setTimeout(waitForTransactionUtils, 100);
            }
        };

        waitForTransactionUtils();
    }

    setupEventListeners() {
        // Customer changes
        $('#customer').on('change', () => {
            if (window.transactionUtils) {
                window.transactionUtils.loadCustomerBalance().then(() => {
                    this.updateAvailableBalance();
                    this.updateTransactionPreview();
                }).catch(err => {
                    console.error('Error loading customer balance:', err);
                });
            }
        });

        // Amount changes
        $('#from_amount').on('input', () => {
            this.checkSufficientBalance();
            this.calculateReceiveAmount();
            this.updateTransactionPreview();
        });

        // Currency changes
        $('#from_currency').on('change', () => {
            this.checkSufficientBalance();
            this.calculateReceiveAmount();
            this.updateTransactionPreview();
        });

        $('#to_currency').on('change', () => {
            this.calculateReceiveAmount();
            this.updateTransactionPreview();
        });

        // Bank and compliance details changes
        $('#destination_country, #bank_name, #swift_code, #account_name').on('input change', () => {
            this.updateTransactionPreview();
        });

        // Commission changes
        $('#commission_amount').on('input', () => {
            this.updateTransactionPreview();
        });
    }

    calculateReceiveAmount() {
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();
        const fromAmount = parseFloat($('#from_amount').val()) || 0;

        if (!fromCurrency || !toCurrency || fromAmount <= 0) {
            $('#to_amount').val('');
            $('#exchange_rate').val('');
            return;
        }

        if (fromCurrency === toCurrency) {
            $('#to_amount').val(fromAmount.toFixed(2));
            $('#exchange_rate').val('1.000000');
            return;
        }

        // Get exchange rate (this would typically come from an API)
        this.getExchangeRate(fromCurrency, toCurrency)
            .then(rate => {
                const toAmount = fromAmount * rate;
                $('#to_amount').val(toAmount.toFixed(2));
                $('#exchange_rate').val(rate.toFixed(6));
            })
            .catch(() => {
                $('#to_amount').val('');
                $('#exchange_rate').val('');
                TransactionUtils.showAlert('warning', 'Unable to get current exchange rate. Please enter manually.');
            });
    }

    getExchangeRate(fromCurrency, toCurrency) {
        return new Promise((resolve, reject) => {
            // This would typically call a real exchange rate API
            // For now, using mock rates
            const mockRates = {
                'USD_EUR': 0.85,
                'USD_GBP': 0.75,
                'USD_JPY': 110.0,
                'EUR_USD': 1.18,
                'EUR_GBP': 0.88,
                'GBP_USD': 1.33,
                'GBP_EUR': 1.14
            };

            const rateKey = `${fromCurrency}_${toCurrency}`;
            const reverseKey = `${toCurrency}_${fromCurrency}`;

            if (mockRates[rateKey]) {
                resolve(mockRates[rateKey]);
            } else if (mockRates[reverseKey]) {
                resolve(1 / mockRates[reverseKey]);
            } else {
                // Default rate if not found
                resolve(1.0);
            }
        });
    }

    updateAvailableBalance() {
        const selectedCurrencyId = $('#from_currency').val();
        if (!selectedCurrencyId || !TransactionUtils.customerBalances) {
            return;
        }

        // Get currency code from the selected option
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];

        // Find balance by currency code
        const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        if (balance) {
            this.currentBalance = balance.balance;
        } else {
            this.currentBalance = 0;
        }
    }

    checkSufficientBalance() {
        const transferAmount = parseFloat($('#from_amount').val()) || 0;
        const selectedCurrencyId = $('#from_currency').val();

        if (!selectedCurrencyId || transferAmount <= 0) {
            $('#balance-warning').empty();
            return;
        }

        // Get currency code from the selected option
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        console.log('Checking balance for currency:', selectedCurrencyCode);

        // Use global TransactionUtils.customerBalances
        const customerBalances = TransactionUtils.customerBalances || [];
        console.log('Available customer balances:', customerBalances);

        const balance = customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        const availableBalance = balance ? parseFloat(balance.balance) : 0;

        console.log('Found balance record:', balance);
        console.log('Available balance:', availableBalance);

        // Show warning if insufficient balance
        let warningHtml = '';
        if (transferAmount > availableBalance) {
            if (!customerBalances || customerBalances.length === 0) {
                warningHtml = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Info:</strong> Customer has no balance records. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else if (!balance) {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Customer has no ${selectedCurrencyCode} balance. Transfer amount: ${transferAmount} ${selectedCurrencyCode}.
                    </div>
                `;
            } else {
                warningHtml = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Transfer amount (${transferAmount}) exceeds available balance (${availableBalance}) for ${selectedCurrencyCode}.
                    </div>
                `;
            }
        }

        $('#balance-warning').html(warningHtml);
    }

    validateForm() {
        let isValid = true;
        const errors = [];

        // Validate amount
        const transferAmount = parseFloat($('#from_amount').val());
        if (transferAmount <= 0) {
            errors.push('Transfer amount must be greater than zero');
            isValid = false;
        }

        // Validate required international details
        const destinationCountry = $('#destination_country').val();
        const bankName = $('#bank_name').val().trim();
        const swiftCode = $('#swift_code').val().trim();
        const accountNumber = $('#account_number').val().trim();
        const accountName = $('#account_name').val().trim();
        const transferPurpose = $('#transfer_purpose').val();
        const complianceDeclaration = $('#compliance_declaration').is(':checked');

        if (!destinationCountry) {
            errors.push('Destination country is required');
            isValid = false;
        }

        if (!bankName) {
            errors.push('Bank name is required');
            isValid = false;
        }

        if (!swiftCode) {
            errors.push('SWIFT code is required');
            isValid = false;
        }

        if (!accountNumber) {
            errors.push('Account number is required');
            isValid = false;
        }

        if (!accountName) {
            errors.push('Beneficiary name is required');
            isValid = false;
        }

        if (!transferPurpose) {
            errors.push('Purpose of transfer is required');
            isValid = false;
        }

        if (!complianceDeclaration) {
            errors.push('Compliance declaration must be accepted');
            isValid = false;
        }

        // Check sufficient balance
        const selectedCurrencyId = $('#from_currency').val();
        if (TransactionUtils.customerBalances && selectedCurrencyId) {
            // Get currency code from the selected option
            const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
            const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
            const availableBalance = balance ? parseFloat(balance.balance) : 0;

            if (transferAmount > availableBalance) {
                errors.push('Insufficient balance for this transfer');
                isValid = false;
            }
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    // Common methods
    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const internationalTransferType = data.results.find(type => type.code === 'INTERNATIONAL_TRANSFER');
                if (internationalTransferType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: internationalTransferType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                TransactionUtils.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter.dataset.action || 'save';
            if (this.validateForm()) {
                this.submitTransaction(action);
            }
        });
    }

    setCurrentUser() {
        const currentUser = ArenaDoviz.auth.getCurrentUser();
        if (currentUser) {
            $('#authorized_by').val(currentUser.display_name || `${currentUser.first_name} ${currentUser.last_name}`);
        }
    }

    getAuthHeaders() {
        // Use the global TransactionUtils method for consistent CSRF handling
        return window.TransactionUtils ? window.TransactionUtils.getAuthHeaders() : {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json',
            'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
        };
    }

    submitTransaction(action) {
        if (!this.validateForm()) {
            return;
        }

        // Set transfer type
        $('<input>').attr({
            type: 'hidden',
            id: 'transfer_type',
            name: 'transfer_type',
            value: 'international'
        }).appendTo(this.form);

        TransactionUtils.submitTransaction(this.form, action)
            .then((response) => {
                TransactionUtils.showAlert('success', 'International transfer created successfully');
                setTimeout(() => {
                    window.location.href = `/transactions/${response.id}/`;
                }, 2000);
            })
            .catch((error) => {
                TransactionUtils.showAlert('danger', `Error creating international transfer: ${error.message}`);
            });
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            fromCurrency: $('#from_currency option:selected').text(),
            toCurrency: $('#to_currency option:selected').text(),
            fromAmount: formData.get('from_amount'),
            toAmount: formData.get('to_amount'),
            exchangeRate: formData.get('exchange_rate'),
            commission: formData.get('commission_amount'),
            country: $('#destination_country option:selected').text(),
            bankName: formData.get('bank_name'),
            beneficiary: formData.get('account_name'),
            purpose: $('#transfer_purpose option:selected').text()
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>From:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.fromAmount && preview.fromCurrency && preview.fromCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Send:</strong> ${preview.fromAmount} ${preview.fromCurrency.split(' - ')[0]}</div>`;
        }
        
        if (preview.toAmount && preview.toCurrency && preview.toCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Receive:</strong> ${preview.toAmount} ${preview.toCurrency.split(' - ')[0]}</div>`;
        }
        
        if (preview.exchangeRate && preview.exchangeRate !== '1.000000') {
            previewHtml += `<div class="list-group-item"><strong>Rate:</strong> ${preview.exchangeRate}</div>`;
        }
        
        if (preview.country && preview.country !== 'Select country...') {
            previewHtml += `<div class="list-group-item"><strong>To:</strong> ${preview.country}</div>`;
        }
        
        if (preview.bankName) {
            previewHtml += `<div class="list-group-item"><strong>Bank:</strong> ${preview.bankName}</div>`;
        }
        
        if (preview.beneficiary) {
            previewHtml += `<div class="list-group-item"><strong>Beneficiary:</strong> ${preview.beneficiary}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.purpose && preview.purpose !== 'Select purpose...') {
            previewHtml += `<div class="list-group-item"><strong>Purpose:</strong> ${preview.purpose}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }
}

// Initialize when DOM is ready
$(document).ready(() => {
    new InternationalTransferForm();
});
