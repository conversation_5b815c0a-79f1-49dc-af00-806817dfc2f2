{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Currency Management" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-currency-exchange"></i>
                {% trans "Currency Management" %}
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-primary" id="add-currency">
                    <i class="bi bi-plus-circle"></i>
                    {% trans "New Currency" %}
                </button>
                <button class="btn btn-outline-secondary" id="currency-stats">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Statistics" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Currency Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-white text-dark metric-card border">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title text-dark">{% trans "Total Currencies" %}</h5>
                        <h2 class="mb-0 text-dark" id="total-currencies">-</h2>
                        <small class="text-muted">{% trans "All currencies" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-exchange fs-1 text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-white text-dark metric-card border">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title text-dark">{% trans "Active Currencies" %}</h5>
                        <h2 class="mb-0 text-dark" id="active-currencies">-</h2>
                        <small class="text-muted">{% trans "Currently active" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle fs-1 text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-white text-dark metric-card border">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title text-dark">{% trans "Base Currency" %}</h5>
                        <h2 class="mb-0 text-dark" id="base-currency">-</h2>
                        <small class="text-muted">{% trans "System base currency" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-star fs-1 text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-white text-dark metric-card border">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title text-dark">{% trans "Exchange Rates" %}</h5>
                        <h2 class="mb-0 text-dark" id="total-rates">-</h2>
                        <small class="text-muted">{% trans "Active exchange rates" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-1 text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-4">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="active">{% trans "Active" %}</option>
                            <option value="inactive">{% trans "Inactive" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="base-currency-filter" class="form-label">{% trans "Base Currency" %}</label>
                        <select class="form-select" id="base-currency-filter" name="is_base_currency">
                            <option value="">{% trans "All Currencies" %}</option>
                            <option value="true">{% trans "Base Currency Only" %}</option>
                            <option value="false">{% trans "Non-Base Currencies" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="search-filter" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search-filter" name="search" placeholder="{% trans 'Search by code or name...' %}">
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear Filters" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Currencies Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "Currency List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="currencies-table">
                        <thead>
                            <tr>
                                <th>{% trans "Code" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Symbol" %}</th>
                                <th>{% trans "Decimal Places" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Sort Order" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Currency Modal -->
<div class="modal fade" id="currencyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currencyModalTitle">{% trans "Currency Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="currency-form">
                    <input type="hidden" id="currency-id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency-code" class="form-label">{% trans "Currency Code" %} *</label>
                                <input type="text" class="form-control" id="currency-code" name="code" required maxlength="3" style="text-transform: uppercase;">
                                <div class="form-text">{% trans "3-letter ISO currency code (e.g., USD, EUR)" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency-name" class="form-label">{% trans "Currency Name" %} *</label>
                                <input type="text" class="form-control" id="currency-name" name="name" required>
                                <div class="form-text">{% trans "Full name of the currency" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency-symbol" class="form-label">{% trans "Currency Symbol" %} *</label>
                                <input type="text" class="form-control" id="currency-symbol" name="symbol" required maxlength="10">
                                <div class="form-text">{% trans "Currency symbol (e.g., $, €, ﷼)" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="decimal-places" class="form-label">{% trans "Decimal Places" %}</label>
                                <select class="form-select" id="decimal-places" name="decimal_places">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2" selected>2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                </select>
                                <div class="form-text">{% trans "Number of decimal places for this currency" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort-order" class="form-label">{% trans "Sort Order" %}</label>
                                <input type="number" class="form-control" id="sort-order" name="sort_order" min="0" value="0">
                                <div class="form-text">{% trans "Display order (lower numbers appear first)" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                                    <label class="form-check-label" for="is-active">
                                        {% trans "Active" %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is-base-currency" name="is_base_currency">
                                    <label class="form-check-label" for="is-base-currency">
                                        {% trans "Base Currency" %}
                                    </label>
                                    <div class="form-text">{% trans "Only one currency can be the base currency" %}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="currency-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="currency-notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-currency">{% trans "Save Currency" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currenciesTable;
let isEditMode = false;

$(document).ready(function() {
    // Initialize page
    loadCurrencyStats();
    initializeCurrenciesTable();

    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        currenciesTable.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        currenciesTable.ajax.reload();
    });

    $('#refresh-btn').on('click', function() {
        currenciesTable.ajax.reload();
        loadCurrencyStats();
    });

    $('#export-btn').on('click', function() {
        exportCurrencies();
    });

    $('#add-currency').on('click', function() {
        showCurrencyModal();
    });

    $('#save-currency').on('click', function() {
        saveCurrency();
    });

    // Auto-uppercase currency code
    $('#currency-code').on('input', function() {
        $(this).val($(this).val().toUpperCase());
    });

    // Base currency warning
    $('#is-base-currency').on('change', function() {
        if ($(this).is(':checked')) {
            showAlert('warning', '{% trans "Setting this as base currency will remove base currency status from other currencies." %}');
        }
    });
});

function loadCurrencyStats() {
    $.ajax({
        url: '/api/v1/currencies/currencies/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const currencies = data.results || [];
            const activeCurrencies = currencies.filter(c => c.is_active);
            const baseCurrency = currencies.find(c => c.is_base_currency);

            $('#total-currencies').text(currencies.length);
            $('#active-currencies').text(activeCurrencies.length);
            $('#base-currency').text(baseCurrency ? baseCurrency.code : '-');
        },
        error: function(xhr) {
            console.error('Error loading currency stats:', xhr);
        }
    });

    // Load exchange rates count
    $.ajax({
        url: '/api/v1/currencies/rates/?is_active=true',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#total-rates').text(data.count || 0);
        }
    });
}

function initializeCurrenciesTable() {
    // Check if DataTable already exists and destroy it
    if ($.fn.DataTable.isDataTable('#currencies-table')) {
        $('#currencies-table').DataTable().destroy();
        console.log('Existing currencies DataTable destroyed');
    }

    const columns = [
        {
            data: 'code',
            name: 'code',
            render: function(data, type, row) {
                return `<code class="fw-bold">${data}</code>`;
            }
        },
        {
            data: 'name',
            name: 'name',
            render: function(data, type, row) {
                const baseCurrencyIcon = row.is_base_currency ? '<i class="bi bi-star-fill text-warning ms-1" title="Base Currency"></i>' : '';
                return `${data}${baseCurrencyIcon}`;
            }
        },
        {
            data: 'symbol',
            name: 'symbol',
            render: function(data, type, row) {
                return `<span class="fw-bold">${data}</span>`;
            }
        },
        {
            data: 'decimal_places',
            name: 'decimal_places',
            render: function(data, type, row) {
                return data || 0;
            }
        },
        {
            data: 'is_active',
            name: 'is_active',
            render: function(data, type, row) {
                const badgeClass = data ? 'bg-success' : 'bg-secondary';
                const text = data ? '{% trans "Active" %}' : '{% trans "Inactive" %}';
                return `<span class="badge ${badgeClass}">${text}</span>`;
            }
        },
        {
            data: 'is_base_currency',
            render: function(data, type, row) {
                return data ? '<span class="badge bg-warning">{% trans "Base Currency" %}</span>' : '<span class="badge bg-primary">{% trans "Regular" %}</span>';
            }
        },
        {
            data: 'sort_order',
            render: function(data, type, row) {
                return data || 0;
            }
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                return `
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewCurrency('${row.id}')" title="{% trans 'View Details' %}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="editCurrency('${row.id}')" title="{% trans 'Edit' %}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${row.is_active ?
                            `<button class="btn btn-sm btn-outline-warning" onclick="deactivateCurrency('${row.id}')" title="{% trans 'Deactivate' %}">
                                <i class="bi bi-pause"></i>
                            </button>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="activateCurrency('${row.id}')" title="{% trans 'Activate' %}">
                                <i class="bi bi-play"></i>
                            </button>`
                        }
                    </div>
                `;
            }
        }
    ];

    currenciesTable = ArenaDoviz.tables.initServerSideTable(
        '#currencies-table',
        '/api/v1/currencies/currencies/',
        columns,
        {
            order: [[6, 'asc'], [0, 'asc']], // Order by sort_order, then code
            ajax: {
                url: '/api/v1/currencies/currencies/',
                type: 'GET',
                headers: getAuthHeaders(),
                data: function(d) {
                    // Add filter parameters
                    const formData = new FormData($('#filter-form')[0]);
                    const filters = {};

                    for (let [key, value] of formData.entries()) {
                        if (value) {
                            filters[key] = value;
                        }
                    }

                    return Object.assign({
                        page: Math.floor(d.start / d.length) + 1,
                        page_size: d.length,
                        search: d.search.value,
                        ordering: d.order.length > 0 ?
                            (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].name :
                            'sort_order,code'
                    }, filters);
                },
                dataSrc: function(json) {
                    json.recordsTotal = json.count;
                    json.recordsFiltered = json.count;
                    return json.results;
                }
            }
        }
    );
}

function showCurrencyModal(currencyData = null) {
    isEditMode = !!currencyData;

    $('#currencyModalTitle').text(isEditMode ? '{% trans "Edit Currency" %}' : '{% trans "Add New Currency" %}');
    $('#save-currency').text(isEditMode ? '{% trans "Update Currency" %}' : '{% trans "Save Currency" %}');

    // Reset form
    $('#currency-form')[0].reset();
    $('#currency-id').val('');

    if (currencyData) {
        // Populate form with currency data
        $('#currency-id').val(currencyData.id);
        $('#currency-code').val(currencyData.code);
        $('#currency-name').val(currencyData.name);
        $('#currency-symbol').val(currencyData.symbol);
        $('#decimal-places').val(currencyData.decimal_places);
        $('#sort-order').val(currencyData.sort_order);
        $('#is-active').prop('checked', currencyData.is_active);
        $('#is-base-currency').prop('checked', currencyData.is_base_currency);
        $('#currency-notes').val(currencyData.notes);
    }

    $('#currencyModal').modal('show');
}

function saveCurrency() {
    const formData = new FormData($('#currency-form')[0]);
    const currencyData = {};

    for (let [key, value] of formData.entries()) {
        if (key === 'is_active' || key === 'is_base_currency') {
            currencyData[key] = value === 'on';
        } else if (value) {
            currencyData[key] = value;
        }
    }

    const currencyId = $('#currency-id').val();
    const url = currencyId ? `/api/v1/currencies/currencies/${currencyId}/` : '/api/v1/currencies/currencies/';
    const method = currencyId ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        headers: getAuthHeaders(),
        data: JSON.stringify(currencyData),
        success: function(data) {
            $('#currencyModal').modal('hide');
            currenciesTable.ajax.reload();
            loadCurrencyStats();
            showAlert('success', isEditMode ? '{% trans "Currency updated successfully!" %}' : '{% trans "Currency created successfully!" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || (isEditMode ? '{% trans "Failed to update currency" %}' : '{% trans "Failed to create currency" %}');
            showAlert('danger', error);
        }
    });
}

function viewCurrency(currencyId) {
    window.location.href = `/currencies/${currencyId}/`;
}

function editCurrency(currencyId) {
    $.ajax({
        url: `/api/v1/currencies/currencies/${currencyId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            showCurrencyModal(data);
        },
        error: function(xhr) {
            showAlert('danger', '{% trans "Failed to load currency details" %}');
        }
    });
}

function deactivateCurrency(currencyId) {
    if (confirm('{% trans "Are you sure you want to deactivate this currency?" %}')) {
        $.ajax({
            url: `/api/v1/currencies/currencies/${currencyId}/`,
            method: 'PATCH',
            headers: getAuthHeaders(),
            data: JSON.stringify({ is_active: false }),
            success: function(data) {
                currenciesTable.ajax.reload();
                loadCurrencyStats();
                showAlert('success', '{% trans "Currency deactivated successfully!" %}');
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '{% trans "Failed to deactivate currency" %}';
                showAlert('danger', error);
            }
        });
    }
}

function activateCurrency(currencyId) {
    $.ajax({
        url: `/api/v1/currencies/currencies/${currencyId}/`,
        method: 'PATCH',
        headers: getAuthHeaders(),
        data: JSON.stringify({ is_active: true }),
        success: function(data) {
            currenciesTable.ajax.reload();
            loadCurrencyStats();
            showAlert('success', '{% trans "Currency activated successfully!" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.message || '{% trans "Failed to activate currency" %}';
            showAlert('danger', error);
        }
    });
}

function exportCurrencies() {
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    params.append('format', 'excel');

    window.open('/api/v1/currencies/currencies/export/?' + params.toString());
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken() || '';
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('main .container-fluid').prepend(alert);

    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
