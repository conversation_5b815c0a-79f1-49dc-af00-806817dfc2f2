"""
Custom SQLite database backend for Arena Doviz with optimized settings.
"""

from django.db.backends.sqlite3.base import DatabaseWrapper as BaseDatabaseWrapper
from django.db import transaction
import logging
import time
import sqlite3

logger = logging.getLogger(__name__)


class DatabaseWrapper(BaseDatabaseWrapper):
    """Custom SQLite database wrapper with optimized settings and better locking handling."""

    def get_new_connection(self, conn_params):
        """Create a new database connection with optimized SQLite settings."""
        conn = super().get_new_connection(conn_params)

        # Configure SQLite for better concurrency and performance
        try:
            with conn:
                # Enable WAL mode for better concurrency
                conn.execute('PRAGMA journal_mode=WAL;')

                # Set synchronous mode to NORMAL for better performance
                conn.execute('PRAGMA synchronous=NORMAL;')

                # Increase cache size for better performance (10MB)
                conn.execute('PRAGMA cache_size=10000;')

                # Store temporary tables in memory
                conn.execute('PRAGMA temp_store=MEMORY;')

                # Set busy timeout to 60 seconds (increased from 30)
                conn.execute('PRAGMA busy_timeout=60000;')

                # Enable foreign key constraints
                conn.execute('PRAGMA foreign_keys=ON;')

                # Optimize for write-heavy workloads
                conn.execute('PRAGMA wal_autocheckpoint=1000;')

                # Additional optimizations for concurrency
                conn.execute('PRAGMA locking_mode=NORMAL;')  # Allow multiple connections
                conn.execute('PRAGMA read_uncommitted=1;')   # Allow dirty reads for better performance

                logger.debug("SQLite connection configured with optimized settings")

        except sqlite3.Error as e:
            logger.error(f"Error configuring SQLite connection: {e}")

        return conn

    def _start_transaction_under_autocommit(self):
        """Override to add retry logic for database locks."""
        max_retries = 3
        retry_delay = 0.1

        for attempt in range(max_retries):
            try:
                return super()._start_transaction_under_autocommit()
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    logger.warning(f"Database locked, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"Failed to start transaction after {max_retries} attempts: {e}")
                    raise

    def _commit(self):
        """Override to add retry logic for commits."""
        max_retries = 3
        retry_delay = 0.1

        for attempt in range(max_retries):
            try:
                return super()._commit()
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    logger.warning(f"Database locked during commit, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"Failed to commit after {max_retries} attempts: {e}")
                    raise
