/**
 * Cash Deposit Transaction Form Handler
 * Handles form validation, verification, and submission for cash deposit transactions
 */

class DepositTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'DEPOSIT';

        // Only initialize if form exists
        if (this.form) {
            this.init();
        } else {
            console.warn('Transaction form not found, skipping DepositTransactionForm initialization');
        }
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
        this.setCurrentUser();
        this.initializeFields();
    }

    initializeFields() {
        // Initialize commission amount field with default value
        if (!$('#commission_amount').val()) {
            $('#commission_amount').val('0.00');
        }
    }

    loadFormData() {
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
        TransactionUtils.loadCouriers();

        // Pre-populate customer from URL parameter if present
        TransactionUtils.prePopulateCustomerFromUrl();
    }

    bindEvents() {
        // Customer selection
        $('#customer').on('change', () => {
            const customerId = $('#customer').val();
            TransactionUtils.loadCustomerBalance();

            // Reload couriers with customer-specific options
            if (customerId) {
                TransactionUtils.loadCouriers(customerId);
            }
        });

        // Amount changes
        $('#from_amount').on('input', () => {
            this.updateTransactionPreview();
        });

        // Deposit source changes
        $('#deposit_source').on('change', () => {
            this.toggleSourceFields();
        });

        // Delivery method changes
        $('#delivery_method').on('change', () => {
            this.toggleCourierField();
        });

        // Verification checkboxes
        $('#cash_verified, #customer_id_verified').on('change', () => {
            this.updateTransactionPreview();
        });

        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        // File upload handling
        $('#document_files').on('change', () => {
            TransactionUtils.handleFileSelection();
        });

        // Real-time form validation and preview updates
        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validateDepositForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateDepositForm() {
        let isValid = true;
        const errors = [];

        // Validate amount
        const amount = parseFloat($('#from_amount').val());
        if (amount <= 0) {
            errors.push('Deposit amount must be greater than zero');
            isValid = false;
        }

        // Validate cash verification
        if (!$('#cash_verified').is(':checked')) {
            errors.push('Cash verification is required for deposit transactions');
            isValid = false;
        }

        // Validate source-specific fields
        const depositSource = $('#deposit_source').val();
        if (depositSource === 'bank_transfer' && !$('#bank_reference').val()) {
            errors.push('Bank reference number is required for bank transfer deposits');
            isValid = false;
        }

        if (depositSource === 'check' && !$('#check_number').val()) {
            errors.push('Check number is required for check deposits');
            isValid = false;
        }

        if (!isValid) {
            this.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const depositType = data.results.find(type => type.code === 'DEPOSIT');
                if (depositType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: depositType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                this.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    loadCustomers() {
        $.ajax({
            url: '/api/v1/customers/customers/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const customerSelect = $('#customer');
                customerSelect.empty().append('<option value="">Select customer...</option>');
                
                data.results.forEach(customer => {
                    customerSelect.append(`<option value="${customer.id}">${customer.first_name} ${customer.last_name}</option>`);
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading customers');
            }
        });
    }

    loadLocations() {
        $.ajax({
            url: '/api/v1/locations/locations/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const locationSelect = $('#location');
                locationSelect.empty().append('<option value="">Select location...</option>');
                
                data.results.forEach(location => {
                    locationSelect.append(`<option value="${location.id}">${location.name}</option>`);
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading locations');
            }
        });
    }

    loadCurrencies() {
        $.ajax({
            url: '/api/v1/currencies/currencies/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const currencySelect = $('#from_currency');
                currencySelect.empty().append('<option value="">Select currency...</option>');
                
                data.results.forEach(currency => {
                    currencySelect.append(`<option value="${currency.id}">${currency.code} - ${currency.name}</option>`);
                });
                
                // Set to_currency same as from_currency for deposits
                $('#from_currency').on('change', () => {
                    const selectedCurrency = $('#from_currency').val();
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'to_currency',
                        name: 'to_currency',
                        value: selectedCurrency
                    }).appendTo(this.form);
                    
                    // Set to_amount same as from_amount for deposits
                    $('#from_amount').on('input', () => {
                        $('#to_amount').val($('#from_amount').val());
                    });
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading currencies');
            }
        });
    }

    loadCustomerBalance() {
        const customerId = $('#customer').val();
        if (!customerId) {
            $('#customer-balance').html('<div class="text-muted text-center py-3"><i class="bi bi-person"></i> Select customer to view balance</div>');
            return;
        }

        $.ajax({
            url: `/api/v1/transactions/api/customer/${customerId}/balance/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                let balanceHtml = '<div class="list-group list-group-flush">';
                
                if (data.balances && data.balances.length > 0) {
                    data.balances.forEach(balance => {
                        const balanceClass = balance.amount >= 0 ? 'text-success' : 'text-danger';
                        balanceHtml += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>${balance.currency_code}</span>
                                <span class="${balanceClass}">${balance.formatted_amount}</span>
                            </div>
                        `;
                    });
                } else {
                    balanceHtml += '<div class="list-group-item text-muted text-center">No balance records found</div>';
                }
                
                balanceHtml += '</div>';
                $('#customer-balance').html(balanceHtml);
            },
            error: () => {
                $('#customer-balance').html('<div class="text-danger text-center py-3"><i class="bi bi-exclamation-triangle"></i> Error loading balance</div>');
            }
        });
    }

    toggleSourceFields() {
        const depositSource = $('#deposit_source').val();

        // Hide all conditional fields
        $('#bank-transfer-details, #check-details').hide();

        // Show relevant fields based on deposit source
        switch (depositSource) {
            case 'bank_transfer':
                $('#bank-transfer-details').show();
                break;
            case 'check':
                $('#check-details').show();
                break;
        }
    }

    toggleCourierField() {
        const deliveryMethod = $('#delivery_method').val();
        const courierField = $('#courier-field');

        if (deliveryMethod === 'courier') {
            courierField.show();
            $('#courier').attr('required', true);
        } else {
            courierField.hide();
            $('#courier').attr('required', false);
            // Clear courier value when field is hidden to prevent validation errors
            $('#courier').val('');
        }
    }

    setCurrentUser() {
        // Set current user as the one who received the cash
        // This would typically come from the authentication system
        $('#received_by').val('Current User'); // Replace with actual user name
        
        // Generate receipt number (this would typically be done on the backend)
        const receiptNumber = 'RCP-' + Date.now();
        $('#receipt_number').val(receiptNumber);
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            amount: formData.get('from_amount'),
            commission: formData.get('commission_amount'),
            depositSource: $('#deposit_source option:selected').text(),
            cashVerified: $('#cash_verified').is(':checked'),
            idVerified: $('#customer_id_verified').is(':checked')
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>Customer:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.amount && preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Deposit:</strong> ${preview.amount} ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Service Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.depositSource && preview.depositSource !== 'Cash') {
            previewHtml += `<div class="list-group-item"><strong>Source:</strong> ${preview.depositSource}</div>`;
        }
        
        // Verification status
        const verificationStatus = [];
        if (preview.cashVerified) verificationStatus.push('Cash Verified');
        if (preview.idVerified) verificationStatus.push('ID Verified');
        
        if (verificationStatus.length > 0) {
            previewHtml += `<div class="list-group-item"><strong>Verification:</strong> ${verificationStatus.join(', ')}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateDepositForm()) {
            return;
        }

        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData.entries());

        // Clean and validate numeric fields
        data.from_amount = parseFloat(data.from_amount) || 0;
        data.to_amount = data.from_amount; // Same as from_amount for deposits
        data.exchange_rate = 1; // No exchange for deposits

        // Handle commission_amount - ensure it's a valid number or null
        if (data.commission_amount === '' || data.commission_amount === undefined || data.commission_amount === null) {
            data.commission_amount = null;
        } else {
            const commissionValue = parseFloat(data.commission_amount);
            data.commission_amount = isNaN(commissionValue) ? null : commissionValue;
        }

        // Set status based on action
        data.status = action === 'submit' ? 'pending' : 'draft';

        // Set to_currency same as from for deposits
        data.to_currency = data.from_currency;

        // Use the delivery method selected by the user (don't hardcode to 'cash')
        // data.delivery_method is already set from the form

        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'POST',
            headers: this.getAuthHeaders(),
            data: JSON.stringify(data),
            success: (response) => {
                const transactionId = response.id;

                // Upload documents if any
                const files = $('#document_files')[0].files;
                if (files.length > 0) {
                    this.uploadDocuments(transactionId, files);
                } else {
                    this.showAlert('success', 'Cash deposit created successfully');
                    setTimeout(() => {
                        window.location.href = `/transactions/${transactionId}/`;
                    }, 2000);
                }
            },
            error: (xhr) => {
                const errors = xhr.responseJSON;
                let errorMessage = 'Error creating cash deposit';

                if (errors) {
                    errorMessage += ':<br>';
                    for (let field in errors) {
                        errorMessage += `${field}: ${errors[field]}<br>`;
                    }
                }

                this.showAlert('danger', errorMessage);
            }
        });
    }

    uploadDocuments(transactionId, files) {
        const formData = new FormData();
        const documentType = $('#document_type').val();

        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        formData.append('document_type', documentType);
        formData.append('transaction', transactionId);

        $.ajax({
            url: '/api/v1/transactions/documents/',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: formData,
            processData: false,
            contentType: false,
            success: () => {
                this.showAlert('success', 'Cash deposit and documents uploaded successfully');
                setTimeout(() => {
                    window.location.href = `/transactions/${transactionId}/`;
                }, 2000);
            },
            error: () => {
                this.showAlert('warning', 'Cash deposit created but document upload failed');
                setTimeout(() => {
                    window.location.href = `/transactions/${transactionId}/`;
                }, 5000);
            }
        });
    }

    handleFileSelection() {
        const files = $('#document_files')[0].files;
        if (files.length > 0) {
            let fileListHtml = '';
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileListHtml += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark"></i>
                            <span class="ms-2">${file.name}</span>
                            <small class="text-muted ms-2">(${fileSize} MB)</small>
                        </div>
                    </div>
                `;
            }
            $('#file-list').html(fileListHtml);
            $('#uploaded-files-preview').show();
        } else {
            $('#uploaded-files-preview').hide();
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        $('.alert').remove();
        $('.row').first().before(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
}

// Global function for commission calculation
function calculateDepositCommission() {
    const amount = parseFloat($('#from_amount').val()) || 0;
    const currency = $('#from_currency').val();
    const location = $('#location').val();

    if (!amount || !currency) {
        TransactionUtils.showAlert('warning', 'Please enter amount and select currency first');
        return;
    }

    TransactionUtils.calculateCommission(amount, 'DEPOSIT', currency, currency, location)
        .then(result => {
            $('#commission_amount').val(result.commission_amount.toFixed(6));
            $('#commission_info').html(`
                <small class="text-muted">
                    Commission: ${result.commission_percentage}% = ${TransactionUtils.formatCurrency(result.commission_amount, currency)}
                </small>
            `);
        })
        .catch(error => {
            console.error('Error calculating commission:', error);
            TransactionUtils.showAlert('danger', 'Error calculating commission');
        });
}

// Initialize when DOM is ready
$(document).ready(() => {
    new DepositTransactionForm();
});
